# High-Priority Lead Sequence Workflow Implementation

## Overview

This document outlines the implementation of the high-priority lead sequence workflow integration for <PERSON>'s real estate wholesaling business. The system now supports immediate communication with agents for hot leads, tier-based lead management, and automated Eric notifications.

## Key Features Implemented

### 1. Enhanced Tier Classification System

**File:** `agents/workflows/tier_classifier.py`

- **Tier 1:** Agent responded positively to Gina + provided property address(es) + tagged as "off-market" if applicable
- **Tier 2:** Made an offer but deal didn't work/get accepted  
- **Tier 3:** DNC (Do Not Contact) - agent responded negatively or specifically requested no contact

**New Functions:**
- `is_hot_lead()` - Identifies leads requiring immediate attention
- `get_tier_description()` - Human-readable tier descriptions
- `should_move_to_tier_2()` - Logic for tier transitions

### 2. Agent Communication Templates

**Location:** `knowledge_pipeline/templates/agent_communication/`

**Templates Created:**
- `hot_lead_email.md` - Immediate email showing interest in hot lead property
- `hot_lead_sms.md` - Short SMS for hot lead property  
- `tier2_biweekly_email.md` - Bi-weekly check-in email for Tier 2 agents
- `tier2_biweekly_sms.md` - Bi-weekly check-in SMS for Tier 2 agents

**Internal Notifications:** `knowledge_pipeline/templates/internal_notifications/`
- `eric_call_request.md` - Request Eric to call an agent
- `hot_lead_alert.md` - Alert Eric about hot leads

### 3. Agent Communication Workflow

**File:** `agents/workflows/agent_communication_workflow.py`

**Key Features:**
- Immediate email and SMS for hot leads
- Template-based communication
- GHL integration for message delivery
- Communication result tracking
- Error handling and fallback templates

**Main Function:** `execute_agent_communication(lead_id, lead_data)`

### 4. Eric Notification Manager

**File:** `agents/workflows/eric_notification_manager.py`

**Capabilities:**
- Hot lead alerts with immediate notification
- Call request routing to Eric
- Agent response notifications
- Internal messaging via GHL SMS
- Priority-based notification handling

**Key Functions:**
- `notify_eric_hot_lead()` - Alert about hot leads
- `request_eric_call()` - Request phone calls
- `notify_agent_response()` - Track agent responses

### 5. Enhanced Lead Processing Pipeline

**File:** `agents/workflows/enhanced_lead_processor.py`

**New Integration:**
- Hot lead detection during processing
- Immediate agent communication for hot leads
- Eric notifications for critical leads
- Seamless workflow integration

### 6. Updated GHL Workflow

**File:** `ghl-lead-workflow.json`

**Enhancements:**
- New routing conditions for hot leads and Tier 3
- Hot lead processing node
- Tier 3 (DNC) handling node
- Enhanced JavaScript logic for tier classification
- Additional workflow connections

### 7. Tier 2 Follow-up Scheduler

**File:** `schedulers/follow_up_scheduler.py`

**New Functions:**
- `run_tier2_biweekly_scheduler()` - Bi-weekly Tier 2 follow-ups
- `get_tier2_leads_for_followup()` - Query Tier 2 leads
- `should_send_tier2_followup()` - Timing logic
- `send_tier2_biweekly_checkin()` - Execute communication
- `update_tier2_followup_timestamp()` - Track follow-ups

### 8. API Endpoints

**File:** `api/routes/workflows.py`

**New Endpoints:**
- `/api/v1/workflows/hot-lead-processing` - Trigger hot lead workflow
- `/api/v1/workflows/eric-call-request` - Request Eric calls

### 9. Workflow Configuration

**File:** `config/workflows.json`

**New Sections:**
- `agent_communication` - Communication settings and templates
- `hot_lead_processing` - Hot lead workflow configuration
- `eric_notifications` - Eric notification settings

## Workflow Process

### Hot Lead Sequence (Tier 1)

1. **Lead Detection:** System identifies hot lead based on indicators
2. **Immediate Email:** Sends professional email showing interest
3. **Immediate SMS:** Sends short SMS for quick response
4. **Eric Notification:** Alerts Eric via SMS about hot lead
5. **Property Analysis:** Triggers comping and MAO calculation
6. **Follow-up Tracking:** Monitors agent response

### Tier 2 Follow-up Sequence

1. **Bi-weekly Schedule:** Every 14 days after offer rejection
2. **Check-in Communication:** Email and SMS asking about new deals
3. **Relationship Building:** Maintains agent relationships
4. **Opportunity Tracking:** Monitors for new property opportunities

### Tier 3 (DNC) Handling

1. **Automatic Detection:** Identifies DNC requests/negative responses
2. **Communication Stop:** Halts all automated communications
3. **List Management:** Adds to DNC list
4. **Compliance:** Ensures no further contact

## Configuration Requirements

### Environment Variables
- `ERIC_CONTACT_ID` - Eric's GHL contact ID for notifications
- `GHL_API_KEY` - GoHighLevel API key
- `SUPABASE_URL` - Database connection
- `SUPABASE_KEY` - Database authentication

### Database Schema Updates
The system expects these fields in the leads table:
- `tier` (integer) - Lead tier (1, 2, or 3)
- `last_tier2_followup` (timestamp) - Last Tier 2 follow-up
- `tier2_followup_count` (integer) - Number of Tier 2 follow-ups
- `offer_made_date` (timestamp) - When offer was made
- `dnc` (boolean) - Do not contact flag

## Usage

### Running Tier 2 Scheduler
```bash
python schedulers/follow_up_scheduler.py tier2
```

### API Usage Examples

**Trigger Hot Lead Processing:**
```bash
curl -X POST http://api:5002/api/v1/workflows/hot-lead-processing \
  -H "Content-Type: application/json" \
  -d '{"lead_id": "123", "priority": "critical"}'
```

**Request Eric Call:**
```bash
curl -X POST http://api:5002/api/v1/workflows/eric-call-request \
  -H "Content-Type: application/json" \
  -d '{"lead_id": "123", "call_reason": "Agent wants to discuss multiple properties"}'
```

## Next Steps

1. **Configure Eric's Contact ID** in the notification manager
2. **Set up Tier 2 scheduler** as a cron job for bi-weekly execution
3. **Test hot lead workflow** with sample data
4. **Monitor agent responses** and adjust templates as needed
5. **Train team** on new tier system and Eric notification process

## Benefits

- **Immediate Response:** Hot leads get instant attention
- **Relationship Management:** Tier 2 agents stay engaged
- **Compliance:** Tier 3 ensures DNC compliance
- **Efficiency:** Automated workflows reduce manual work
- **Scalability:** System handles increasing lead volume
- **Tracking:** Complete audit trail of communications

This implementation provides a comprehensive solution for managing agent relationships and ensuring hot leads receive immediate attention while maintaining ongoing relationships with all agent contacts.
