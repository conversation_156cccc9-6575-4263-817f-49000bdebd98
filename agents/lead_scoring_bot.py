import logging
import json
from typing import Dict, Any, Optional, List

from agents.agent_base import AgentB<PERSON>
from agents.workflows.tier_classifier import classify_lead_tier, get_tier_criteria

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LeadScoringBot(AgentBase):
    """
    Bot responsible for scoring and classifying leads based on tier criteria.
    Determines if a lead should be processed further or deprioritized.
    """
    
    def __init__(self, lead_id: str, entity: str = "lead"):
        super().__init__(agent_name="lead_scoring_bot", lead_id=lead_id, entity=entity)
        self.tier_criteria = get_tier_criteria()
        self.lead_data = self.context.get("lead", {})
        
    def run(self) -> Dict[str, Any]:
        """
        Main execution method for the lead scoring bot.
        
        Returns:
            Dict containing the scoring results with tier classification and next steps
        """
        logger.info(f"Running lead scoring for lead ID: {self.lead_id}")
        
        # Check if we have valid lead data
        if not self.lead_data:
            error_msg = f"No lead data found for lead ID: {self.lead_id}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "tier": None,
                "next_steps": []
            }
        
        # Classify the lead tier
        is_tier_1 = classify_lead_tier(self.lead_data)
        tier = 1 if is_tier_1 else 2
        
        # Generate detailed scoring explanation
        scoring_explanation = self._generate_scoring_explanation(tier)
        
        # Determine next steps based on tier
        next_steps = self._determine_next_steps(tier)
        
        # Log the scoring result to memory
        self._log_scoring_result(tier, scoring_explanation)
        
        # Update lead tier in datastore
        self._update_lead_tier(tier)
        
        # Return the scoring results
        return {
            "success": True,
            "tier": tier,
            "explanation": scoring_explanation,
            "next_steps": next_steps
        }
    
    def _generate_scoring_explanation(self, tier: int) -> str:
        """
        Generate a detailed explanation of why the lead was classified as the given tier.
        
        Args:
            tier: The tier classification (1 or 2)
            
        Returns:
            String explanation of the scoring decision
        """
        # Extract relevant fields from lead data
        property_value = self._extract_numeric_value(self.lead_data.get("property_value", 0))
        equity = self._extract_numeric_value(self.lead_data.get("equity", 0))
        
        # Combine various text fields for motivation analysis
        notes = self.lead_data.get("notes", "").lower()
        situation = self.lead_data.get("situation", "").lower()
        motivation = self.lead_data.get("motivation", "").lower()
        comments = self.lead_data.get("comments", "").lower()
        
        combined_text = f"{notes} {situation} {motivation} {comments}"
        
        # Find motivation indicators
        motivation_indicators = []
        if "motivated_seller_indicators" in self.tier_criteria:
            for indicator in self.tier_criteria["motivated_seller_indicators"]:
                if indicator in combined_text:
                    motivation_indicators.append(indicator)
        
        # Generate explanation based on tier
        if tier == 1:
            explanation = f"Lead classified as Tier 1 (High Priority).\n"
            explanation += f"Property value: ${property_value:,.2f}\n"
            explanation += f"Estimated equity: ${equity:,.2f}\n"
            
            if motivation_indicators:
                explanation += f"Motivation indicators found: {', '.join(motivation_indicators)}\n"
            
            explanation += "This lead meets all criteria for high-priority processing."
        else:
            explanation = f"Lead classified as Tier 2 (Standard Priority).\n"
            explanation += f"Property value: ${property_value:,.2f}\n"
            explanation += f"Estimated equity: ${equity:,.2f}\n"
            
            # Explain why it's not Tier 1
            reasons = []
            
            if "min_property_value" in self.tier_criteria and property_value < self.tier_criteria["min_property_value"]:
                reasons.append(f"Property value (${property_value:,.2f}) is below the Tier 1 threshold (${self.tier_criteria['min_property_value']:,.2f})")
            
            if "max_property_value" in self.tier_criteria and property_value > self.tier_criteria["max_property_value"]:
                reasons.append(f"Property value (${property_value:,.2f}) is above the Tier 1 threshold (${self.tier_criteria['max_property_value']:,.2f})")
            
            if "min_equity" in self.tier_criteria and equity < self.tier_criteria["min_equity"]:
                reasons.append(f"Equity (${equity:,.2f}) is below the Tier 1 threshold (${self.tier_criteria['min_equity']:,.2f})")
            
            if not motivation_indicators and "motivated_seller_indicators" in self.tier_criteria:
                reasons.append("No clear motivation indicators found in lead data")
            
            if reasons:
                explanation += "Reasons for Tier 2 classification:\n- " + "\n- ".join(reasons)
            else:
                explanation += "Lead does not meet all criteria for Tier 1 classification."
        
        return explanation
    
    def _determine_next_steps(self, tier: int) -> List[str]:
        """
        Determine the next steps for processing the lead based on its tier.
        
        Args:
            tier: The tier classification (1 or 2)
            
        Returns:
            List of next steps to take for this lead
        """
        if tier == 1:
            return [
                "Run comping workflow to determine property value",
                "Calculate MAO (Maximum Allowable Offer)",
                "Generate and send initial offer",
                "Schedule follow-up within 24 hours"
            ]
        else:
            return [
                "Add to nurture campaign",
                "Schedule follow-up in 7 days",
                "Monitor for changes in situation"
            ]
    
    def _log_scoring_result(self, tier: int, explanation: str) -> None:
        """
        Log the scoring result to the agent's memory.
        
        Args:
            tier: The tier classification (1 or 2)
            explanation: The detailed explanation of the scoring
        """
        memory = f"Lead {self.lead_id} classified as Tier {tier}. {explanation}"
        # Higher salience for Tier 1 leads
        salience = 0.9 if tier == 1 else 0.7
        self.log(memory=memory, salience=salience)
    
    def _update_lead_tier(self, tier: int) -> Dict[str, Any]:
        """
        Update the lead's tier classification in the datastore.
        
        Args:
            tier: The tier classification (1 or 2)
            
        Returns:
            Result of the datastore operation
        """
        instruction = f"Update lead {self.lead_id} with tier classification {tier}"
        params = {
            "lead_id": self.lead_id,
            "tier": tier,
            "last_scored_at": "NOW()"
        }
        
        result = self.datastore_operation(instruction, params)
        
        if not result.get("success", False):
            logger.error(f"Failed to update lead tier in datastore: {result.get('error', 'Unknown error')}")
        
        return result
    
    @staticmethod
    def _extract_numeric_value(value) -> float:
        """
        Extract a numeric value from a string or number.
        
        Args:
            value: The value to extract a number from
            
        Returns:
            The extracted float value
        """
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            try:
                # Remove currency symbols and commas
                cleaned_value = value.replace('$', '').replace(',', '')
                return float(cleaned_value)
            except ValueError:
                return 0.0
        
        return 0.0


def score_lead(lead_id: str) -> Dict[str, Any]:
    """
    Convenience function to score a lead by ID.
    
    Args:
        lead_id: The ID of the lead to score
        
    Returns:
        Dict containing the scoring results
    """
    bot = LeadScoringBot(lead_id=lead_id)
    return bot.run()


def batch_score_leads(lead_ids: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Score multiple leads in batch.
    
    Args:
        lead_ids: List of lead IDs to score
        
    Returns:
        Dict mapping lead IDs to their scoring results
    """
    results = {}
    
    for lead_id in lead_ids:
        try:
            results[lead_id] = score_lead(lead_id)
        except Exception as e:
            logger.error(f"Error scoring lead {lead_id}: {str(e)}")
            results[lead_id] = {
                "success": False,
                "error": str(e),
                "tier": None,
                "next_steps": []
            }
    
    return results


class RealtorLeadScoringBot(LeadScoringBot):
    """Specialized bot for scoring realtor-sourced leads"""
    
    def __init__(self):
        super().__init__()
        self.tier_criteria = {
            "tier1": "active_deal OR past_deal",
            "tier2": "provided_address AND not_viable",
            "tier3": "no_current_property BUT open_to_followup"
        }
    
    def score_realtor_lead(self, lead_data):
        """Score realtor leads and assign appropriate tier"""
        # Determine tier based on relationship status
        if lead_data.get("active_deal") or lead_data.get("past_deal"):
            tier = "tier1"
        elif lead_data.get("provided_address"):
            tier = "tier2"
        else:
            tier = "tier3"
            
        # Add to appropriate follow-up sequence
        self._add_to_followup_sequence(lead_data, tier)
        
        return {
            "tier": tier,
            "score": self._calculate_engagement_score(lead_data),
            "followup_schedule": self._get_followup_schedule(tier)
        }
