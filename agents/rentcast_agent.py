#!/usr/bin/env python3
"""
RentCast Agent - Property Data and Comparables API Integration
Provides comprehensive property data, comparables, and valuations using RentCast API
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from agents.agent_base import Agent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class RentCastConfig:
    """Configuration for RentCast API"""
    api_key: str
    base_url: str = "https://api.rentcast.io/v1"
    timeout: int = 30
    max_retries: int = 3

class RentCastAgent(Agent):
    """
    RentCast Agent for property data and comparables
    
    Features:
    - Property records and history
    - Automated valuation models (AVM)
    - Comparable sales data
    - Rental estimates
    - Market statistics
    """
    
    def __init__(self, lead_id: str = "rentcast_agent", entity: str = "property_data"):
        super().__init__("rentcast_agent", lead_id, entity)
        
        # Initialize RentCast configuration
        api_key = os.getenv('RENTCAST_API_KEY')
        if not api_key:
            raise ValueError("RENTCAST_API_KEY environment variable is required")
        
        self.config = RentCastConfig(api_key=api_key)
        self.session = None
        
        logger.info("RentCast Agent initialized successfully")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            headers={'X-Api-Key': self.config.api_key}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def run(self):
        """Required by Agent base class - not used for async operations"""
        pass
    
    async def get_property_comps(self, property_data: Dict[str, Any], 
                               radius_miles: float = 1.0, limit: int = 10) -> Dict[str, Any]:
        """
        Get comparable properties using RentCast Property Records API
        
        Args:
            property_data: Property information including address, city, state
            radius_miles: Search radius in miles (max 100)
            limit: Maximum number of results (max 500)
            
        Returns:
            Dict containing success status, comps data, and metadata
        """
        try:
            # Format address for API
            address = self._format_address(property_data)
            
            params = {
                'address': address,
                'radius': min(radius_miles, 100),  # API max is 100 miles
                'limit': min(limit, 500),  # API max is 500
                'saleDateRange': '2022-01-01,2024-12-31'  # Recent sales only
            }
            
            # Add property type filter if available
            property_type = property_data.get('property_type')
            if property_type:
                params['propertyType'] = self._normalize_property_type(property_type)
            
            logger.info(f"Getting comps for {address} within {radius_miles} miles")
            
            result = await self._make_api_request('/properties', params)
            
            if result['success']:
                # Normalize the property data
                normalized_comps = self._normalize_property_records(result['data'])
                
                return {
                    'success': True,
                    'comps': normalized_comps,
                    'total_found': len(normalized_comps),
                    'source': 'rentcast_api',
                    'search_params': params,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error getting property comps: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'rentcast_api'
            }
    
    async def get_property_value_estimate(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get automated valuation model (AVM) estimate for a property
        
        Args:
            property_data: Property information
            
        Returns:
            Dict containing value estimate and confidence data
        """
        try:
            address = self._format_address(property_data)
            
            params = {'address': address}
            
            # Add property details if available
            if property_data.get('beds'):
                params['bedrooms'] = property_data['beds']
            if property_data.get('baths'):
                params['bathrooms'] = property_data['baths']
            if property_data.get('sqft'):
                params['squareFootage'] = property_data['sqft']
            
            logger.info(f"Getting value estimate for {address}")
            
            result = await self._make_api_request('/avm/value', params)
            
            if result['success']:
                data = result['data']
                
                return {
                    'success': True,
                    'value_estimate': data.get('value'),
                    'confidence_score': data.get('confidence'),
                    'value_range': {
                        'low': data.get('valueLow'),
                        'high': data.get('valueHigh')
                    },
                    'comparables_used': len(data.get('comparables', [])),
                    'source': 'rentcast_avm',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error getting value estimate: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'rentcast_avm'
            }
    
    async def get_rent_estimate(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get rental estimate for a property
        
        Args:
            property_data: Property information
            
        Returns:
            Dict containing rent estimate and market data
        """
        try:
            address = self._format_address(property_data)
            
            params = {'address': address}
            
            # Add property details if available
            if property_data.get('beds'):
                params['bedrooms'] = property_data['beds']
            if property_data.get('baths'):
                params['bathrooms'] = property_data['baths']
            if property_data.get('sqft'):
                params['squareFootage'] = property_data['sqft']
            
            logger.info(f"Getting rent estimate for {address}")
            
            result = await self._make_api_request('/avm/rent/long-term', params)
            
            if result['success']:
                data = result['data']
                
                return {
                    'success': True,
                    'rent_estimate': data.get('rent'),
                    'rent_range': {
                        'low': data.get('rentLow'),
                        'high': data.get('rentHigh')
                    },
                    'confidence_score': data.get('confidence'),
                    'comparables_used': len(data.get('comparables', [])),
                    'source': 'rentcast_rent_estimate',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error getting rent estimate: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'rentcast_rent_estimate'
            }
    
    async def _make_api_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make API request to RentCast with retry logic
        
        Args:
            endpoint: API endpoint path
            params: Query parameters
            
        Returns:
            Dict containing response data or error information
        """
        url = f"{self.config.base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                if not self.session:
                    raise RuntimeError("Session not initialized. Use async context manager.")
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'success': True,
                            'data': data,
                            'status_code': response.status
                        }
                    elif response.status == 401:
                        return {
                            'success': False,
                            'error': 'Invalid API key or subscription inactive',
                            'status_code': response.status
                        }
                    elif response.status == 429:
                        # Rate limited - wait and retry
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'API error: {response.status} - {error_text}',
                            'status_code': response.status
                        }
                        
            except asyncio.TimeoutError:
                logger.warning(f"Request timeout on attempt {attempt + 1}")
                if attempt == self.config.max_retries - 1:
                    return {
                        'success': False,
                        'error': 'Request timeout after retries'
                    }
            except Exception as e:
                logger.error(f"Request error on attempt {attempt + 1}: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    return {
                        'success': False,
                        'error': f'Request failed: {str(e)}'
                    }
        
        return {
            'success': False,
            'error': 'Max retries exceeded'
        }
    
    def _format_address(self, property_data: Dict[str, Any]) -> str:
        """Format address for API request"""
        address_parts = []
        
        if property_data.get('address'):
            address_parts.append(property_data['address'])
        if property_data.get('city'):
            address_parts.append(property_data['city'])
        if property_data.get('state'):
            address_parts.append(property_data['state'])
        if property_data.get('zip_code'):
            address_parts.append(str(property_data['zip_code']))
        
        return ', '.join(address_parts)
    
    def _normalize_property_type(self, property_type: str) -> str:
        """Normalize property type for RentCast API"""
        property_type = property_type.lower()
        
        # Map common property types to RentCast format
        type_mapping = {
            'single family': 'Single Family',
            'single-family': 'Single Family',
            'sfh': 'Single Family',
            'condo': 'Condo',
            'condominium': 'Condo',
            'townhouse': 'Townhouse',
            'townhome': 'Townhouse',
            'duplex': 'Multi-Family',
            'triplex': 'Multi-Family',
            'multi-family': 'Multi-Family',
            'multifamily': 'Multi-Family',
            'apartment': 'Multi-Family'
        }
        
        return type_mapping.get(property_type, 'Single Family')
    
    def _normalize_property_records(self, properties: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize property records to standard format"""
        normalized = []
        
        for prop in properties:
            # Extract address information
            address_line1 = prop.get('addressLine1', '')
            address_line2 = prop.get('addressLine2', '')
            full_address = f"{address_line1} {address_line2}".strip()
            
            # Get the most recent sale
            sale_history = prop.get('saleHistory', [])
            latest_sale = sale_history[0] if sale_history else {}
            
            normalized_prop = {
                'address': full_address or prop.get('address', ''),
                'city': prop.get('city', ''),
                'state': prop.get('state', ''),
                'zip_code': prop.get('zipCode', ''),
                'sale_price': latest_sale.get('amount'),
                'sale_date': latest_sale.get('date'),
                'beds': prop.get('bedrooms'),
                'baths': prop.get('bathrooms'),
                'sqft': prop.get('squareFootage'),
                'lot_size': prop.get('lotSize'),
                'year_built': prop.get('yearBuilt'),
                'property_type': prop.get('propertyType', '').lower(),
                'last_sale_amount': latest_sale.get('amount'),
                'last_sale_date': latest_sale.get('date'),
                'assessed_value': prop.get('taxAssessments', {}).get('2023', {}).get('value'),
                'source': 'rentcast_api',
                'confidence_score': 0.85,  # RentCast generally has high-quality data
                'raw_data': prop  # Keep original data for debugging
            }
            
            normalized.append(normalized_prop)
        
        return normalized

# Utility functions for easy integration
async def get_rentcast_comps(property_data: Dict[str, Any], 
                           radius_miles: float = 1.0, limit: int = 10) -> Dict[str, Any]:
    """
    Get comps from RentCast API
    
    Args:
        property_data: Property information
        radius_miles: Search radius in miles
        limit: Maximum number of results
        
    Returns:
        Dict containing comps data
    """
    async with RentCastAgent() as agent:
        return await agent.get_property_comps(property_data, radius_miles, limit)

async def get_rentcast_value_estimate(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get value estimate from RentCast AVM
    
    Args:
        property_data: Property information
        
    Returns:
        Dict containing value estimate
    """
    async with RentCastAgent() as agent:
        return await agent.get_property_value_estimate(property_data)

async def get_rentcast_rent_estimate(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get rent estimate from RentCast
    
    Args:
        property_data: Property information
        
    Returns:
        Dict containing rent estimate
    """
    async with RentCastAgent() as agent:
        return await agent.get_rent_estimate(property_data)
