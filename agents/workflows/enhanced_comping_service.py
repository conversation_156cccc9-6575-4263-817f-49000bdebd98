"""
Enhanced Comping Service with Multiple Data Sources
Combines API-based sources with selective scraping for comprehensive property analysis
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import aiohttp
import requests

from agents.workflows.comping_workflow import CompingWorkflow
from scrapers.scraper_factory import ScraperFactory

logger = logging.getLogger(__name__)

class EnhancedCompingService:
    """
    Enhanced comping service that uses multiple data sources:
    1. API-based sources (faster, more reliable)
    2. Selective scraping (when APIs don't have data)
    3. Cached results to minimize scraping
    """
    
    def __init__(self):
        self.api_sources = {
            "rentspree": self._get_rentspree_comps,
            "realty_mole": self._get_realty_mole_comps,
            "smarty_streets": self._get_smarty_streets_data
        }
        self.scraper_sources = {
            "batchleads": "batchleads",
            "privy": "privy", 
            "lotside": "lotside"
        }
        self.cache = {}
        self.cache_duration = timedelta(hours=24)
        
    async def get_comprehensive_comps(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive comps using hybrid approach
        """
        address = property_data.get('address', '')
        cache_key = self._generate_cache_key(property_data)
        
        # Check cache first
        if self._is_cached(cache_key):
            logger.info(f"Using cached comps for {address}")
            return self.cache[cache_key]['data']
        
        logger.info(f"Getting comprehensive comps for {address}")
        
        # Step 1: Try API sources first (fast and reliable)
        api_results = await self._get_api_comps(property_data)
        
        # Step 2: If API results are insufficient, use selective scraping
        scraper_results = []
        if len(api_results.get('comps', [])) < 3:
            logger.info("API results insufficient, using selective scraping")
            scraper_results = await self._get_scraper_comps(property_data)
        
        # Step 3: Combine and normalize results
        combined_results = self._combine_results(api_results, scraper_results, property_data)
        
        # Cache the results
        self._cache_results(cache_key, combined_results)
        
        return combined_results
    
    async def _get_api_comps(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get comps from API sources"""
        all_comps = []
        
        for source_name, source_func in self.api_sources.items():
            try:
                logger.info(f"Trying API source: {source_name}")
                result = await source_func(property_data)
                if result.get('success') and result.get('comps'):
                    all_comps.extend(result['comps'])
                    logger.info(f"Got {len(result['comps'])} comps from {source_name}")
            except Exception as e:
                logger.warning(f"API source {source_name} failed: {str(e)}")
        
        return {
            'success': len(all_comps) > 0,
            'comps': all_comps,
            'source': 'api_combined'
        }
    
    async def _get_scraper_comps(self, property_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get comps using selective scraping with session management"""
        scraper_results = []
        
        # Only use BatchLeads scraper for now (most reliable)
        try:
            logger.info("Using BatchLeads scraper as fallback")
            factory = ScraperFactory()
            scraper = await factory.get_scraper("batchleads")
            
            if scraper:
                # Use session management for efficiency
                await scraper.launch_browser()
                
                # Login once and reuse session
                login_success = await scraper.login()
                if login_success:
                    # Search and extract comps
                    search_result = await scraper.search_property(
                        address=property_data.get('address'),
                        city=property_data.get('city'),
                        state=property_data.get('state'),
                        zip_code=property_data.get('zip_code')
                    )
                    
                    if search_result.get('success'):
                        comps = await scraper.extract_comps(search_result)
                        scraper_results.extend(comps)
                        logger.info(f"Got {len(comps)} comps from BatchLeads scraper")
                
                await scraper.close()
                
        except Exception as e:
            logger.error(f"Scraper fallback failed: {str(e)}")
        
        return scraper_results
    
    async def _get_rentspree_comps(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get comps from RentSpree API (example implementation)"""
        try:
            # This is a placeholder - you'd need to implement actual API calls
            # RentSpree has property data APIs that might be accessible
            api_key = os.getenv('RENTSPREE_API_KEY')
            if not api_key:
                return {'success': False, 'error': 'No API key'}
            
            # Implement actual API call here
            return {'success': False, 'error': 'Not implemented yet'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _get_realty_mole_comps(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get comps from RealtyMole API"""
        try:
            api_key = os.getenv('REALTY_MOLE_API_KEY')
            if not api_key:
                return {'success': False, 'error': 'No API key'}
            
            # RealtyMole has a comparables API
            address = f"{property_data.get('address')}, {property_data.get('city')}, {property_data.get('state')}"
            
            async with aiohttp.ClientSession() as session:
                url = "https://api.realtymole.com/v1/properties/comparables"
                headers = {'X-API-Key': api_key}
                params = {
                    'address': address,
                    'radius': '1',  # 1 mile radius
                    'limit': '10'
                }
                
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        comps = self._normalize_realty_mole_data(data)
                        return {'success': True, 'comps': comps}
                    else:
                        return {'success': False, 'error': f'API error: {response.status}'}
                        
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _get_smarty_streets_data(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get property validation from SmartyStreets"""
        try:
            auth_id = os.getenv('SMARTY_STREETS_AUTH_ID')
            auth_token = os.getenv('SMARTY_STREETS_AUTH_TOKEN')
            
            if not auth_id or not auth_token:
                return {'success': False, 'error': 'No API credentials'}
            
            # SmartyStreets for address validation and property data
            # This helps ensure we have accurate property information
            return {'success': False, 'error': 'Not implemented yet'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _normalize_realty_mole_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Normalize RealtyMole API data to standard format"""
        comps = []
        
        for prop in data.get('properties', []):
            comp = {
                'address': prop.get('address'),
                'city': prop.get('city'),
                'state': prop.get('state'),
                'zip_code': prop.get('zipCode'),
                'sale_price': prop.get('price'),
                'sale_date': prop.get('lastSaleDate'),
                'beds': prop.get('bedrooms'),
                'baths': prop.get('bathrooms'),
                'sqft': prop.get('squareFootage'),
                'lot_size': prop.get('lotSize'),
                'year_built': prop.get('yearBuilt'),
                'property_type': prop.get('propertyType'),
                'source': 'realty_mole_api'
            }
            comps.append(comp)
        
        return comps
    
    def _combine_results(self, api_results: Dict[str, Any], scraper_results: List[Dict[str, Any]], 
                        property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Combine API and scraper results"""
        all_comps = []
        
        # Add API comps
        if api_results.get('comps'):
            all_comps.extend(api_results['comps'])
        
        # Add scraper comps
        all_comps.extend(scraper_results)
        
        # Remove duplicates and sort by relevance
        unique_comps = self._deduplicate_comps(all_comps)
        sorted_comps = self._sort_comps_by_relevance(unique_comps, property_data)
        
        return {
            'success': len(sorted_comps) > 0,
            'comps': sorted_comps[:10],  # Limit to top 10
            'total_found': len(sorted_comps),
            'sources_used': self._get_sources_used(all_comps),
            'timestamp': datetime.now().isoformat()
        }
    
    def _deduplicate_comps(self, comps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate comps based on address"""
        seen_addresses = set()
        unique_comps = []
        
        for comp in comps:
            address_key = f"{comp.get('address', '')}-{comp.get('city', '')}"
            if address_key not in seen_addresses:
                seen_addresses.add(address_key)
                unique_comps.append(comp)
        
        return unique_comps
    
    def _sort_comps_by_relevance(self, comps: List[Dict[str, Any]], property_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sort comps by relevance to subject property"""
        # Implement sorting logic based on distance, size similarity, etc.
        return sorted(comps, key=lambda x: self._calculate_relevance_score(x, property_data), reverse=True)
    
    def _calculate_relevance_score(self, comp: Dict[str, Any], property_data: Dict[str, Any]) -> float:
        """Calculate relevance score for a comp"""
        score = 0.0
        
        # Add scoring logic based on:
        # - Distance (if available)
        # - Size similarity
        # - Age of sale
        # - Property type match
        
        return score
    
    def _get_sources_used(self, comps: List[Dict[str, Any]]) -> List[str]:
        """Get list of sources used"""
        sources = set()
        for comp in comps:
            if comp.get('source'):
                sources.add(comp['source'])
        return list(sources)
    
    def _generate_cache_key(self, property_data: Dict[str, Any]) -> str:
        """Generate cache key for property"""
        address = property_data.get('address', '')
        city = property_data.get('city', '')
        state = property_data.get('state', '')
        return f"{address}-{city}-{state}".lower().replace(' ', '-')
    
    def _is_cached(self, cache_key: str) -> bool:
        """Check if results are cached and still valid"""
        if cache_key not in self.cache:
            return False
        
        cached_time = self.cache[cache_key]['timestamp']
        return datetime.now() - cached_time < self.cache_duration
    
    def _cache_results(self, cache_key: str, results: Dict[str, Any]):
        """Cache results"""
        self.cache[cache_key] = {
            'data': results,
            'timestamp': datetime.now()
        }

# Factory function for easy integration
async def get_enhanced_comps(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get enhanced comps using the hybrid approach
    """
    service = EnhancedCompingService()
    return await service.get_comprehensive_comps(property_data)
