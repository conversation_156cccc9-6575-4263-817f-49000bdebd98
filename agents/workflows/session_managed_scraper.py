"""
Session-Managed Scraper Service
Maintains persistent browser sessions to reduce login overhead and improve efficiency
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import pickle

from scrapers.scraper_factory import ScraperFactory
from scrapers.browser_automation import BrowserAutomation

logger = logging.getLogger(__name__)

class SessionManagedScraper:
    """
    Manages persistent browser sessions for efficient scraping
    """
    
    def __init__(self):
        self.active_sessions = {}
        self.session_timeouts = {}
        self.max_session_duration = timedelta(hours=2)
        self.session_dir = "./.browser_sessions"
        self.ensure_session_dir()
        
    def ensure_session_dir(self):
        """Ensure session directory exists"""
        if not os.path.exists(self.session_dir):
            os.makedirs(self.session_dir)
    
    async def get_comps_with_session(self, property_data: Dict[str, Any], 
                                   source: str = "batchleads") -> Dict[str, Any]:
        """
        Get comps using session management for efficiency
        """
        try:
            # Get or create session
            scraper = await self._get_or_create_session(source)
            
            if not scraper:
                return {"success": False, "error": f"Failed to create session for {source}"}
            
            # Perform the scraping
            result = await self._scrape_with_session(scraper, property_data, source)
            
            # Update session timeout
            self._update_session_timeout(source)
            
            return result
            
        except Exception as e:
            logger.error(f"Session-managed scraping failed: {str(e)}")
            # Clean up failed session
            await self._cleanup_session(source)
            return {"success": False, "error": str(e)}
    
    async def _get_or_create_session(self, source: str) -> Optional[BrowserAutomation]:
        """Get existing session or create new one"""
        
        # Check if we have an active session
        if source in self.active_sessions:
            if self._is_session_valid(source):
                logger.info(f"Reusing existing session for {source}")
                return self.active_sessions[source]
            else:
                logger.info(f"Session expired for {source}, creating new one")
                await self._cleanup_session(source)
        
        # Create new session
        logger.info(f"Creating new session for {source}")
        return await self._create_new_session(source)
    
    async def _create_new_session(self, source: str) -> Optional[BrowserAutomation]:
        """Create a new browser session"""
        try:
            factory = ScraperFactory()
            scraper = await factory.get_scraper(source)
            
            if not scraper:
                return None
            
            # Launch browser with session persistence
            await scraper.launch_browser()
            
            # Load saved session if available
            session_file = os.path.join(self.session_dir, f"{source}_session.json")
            if os.path.exists(session_file):
                try:
                    await self._load_session_state(scraper, session_file)
                    logger.info(f"Loaded saved session state for {source}")
                except Exception as e:
                    logger.warning(f"Failed to load session state: {str(e)}")
            
            # Attempt login
            login_success = await scraper.login()
            
            if login_success:
                # Save session state
                await self._save_session_state(scraper, session_file)
                
                # Store in active sessions
                self.active_sessions[source] = scraper
                self.session_timeouts[source] = datetime.now()
                
                logger.info(f"Successfully created and logged in session for {source}")
                return scraper
            else:
                await scraper.close()
                return None
                
        except Exception as e:
            logger.error(f"Failed to create session for {source}: {str(e)}")
            return None
    
    async def _scrape_with_session(self, scraper: BrowserAutomation, 
                                 property_data: Dict[str, Any], source: str) -> Dict[str, Any]:
        """Perform scraping with existing session"""
        try:
            # Search for property
            search_result = await scraper.search_property(
                address=property_data.get('address'),
                city=property_data.get('city'),
                state=property_data.get('state'),
                zip_code=property_data.get('zip_code')
            )
            
            if not search_result.get('success'):
                return search_result
            
            # Extract comps
            comps = await scraper.extract_comps(search_result)
            
            return {
                "success": True,
                "comps": comps,
                "source": source,
                "property_found": search_result.get('property_found', False),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Scraping failed with session: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _load_session_state(self, scraper: BrowserAutomation, session_file: str):
        """Load saved session state (cookies, local storage, etc.)"""
        try:
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            # Load cookies
            if 'cookies' in session_data and scraper.page:
                await scraper.page.context.add_cookies(session_data['cookies'])
            
            # Load local storage
            if 'local_storage' in session_data and scraper.page:
                for key, value in session_data['local_storage'].items():
                    await scraper.page.evaluate(f"localStorage.setItem('{key}', '{value}')")
            
        except Exception as e:
            logger.warning(f"Failed to load session state: {str(e)}")
    
    async def _save_session_state(self, scraper: BrowserAutomation, session_file: str):
        """Save session state for reuse"""
        try:
            session_data = {}
            
            if scraper.page:
                # Save cookies
                cookies = await scraper.page.context.cookies()
                session_data['cookies'] = cookies
                
                # Save local storage
                local_storage = await scraper.page.evaluate("""
                    () => {
                        const storage = {};
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            storage[key] = localStorage.getItem(key);
                        }
                        return storage;
                    }
                """)
                session_data['local_storage'] = local_storage
            
            # Save to file
            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Failed to save session state: {str(e)}")
    
    def _is_session_valid(self, source: str) -> bool:
        """Check if session is still valid"""
        if source not in self.session_timeouts:
            return False
        
        session_age = datetime.now() - self.session_timeouts[source]
        return session_age < self.max_session_duration
    
    def _update_session_timeout(self, source: str):
        """Update session timeout"""
        self.session_timeouts[source] = datetime.now()
    
    async def _cleanup_session(self, source: str):
        """Clean up session"""
        if source in self.active_sessions:
            try:
                await self.active_sessions[source].close()
            except Exception as e:
                logger.warning(f"Error closing session: {str(e)}")
            
            del self.active_sessions[source]
        
        if source in self.session_timeouts:
            del self.session_timeouts[source]
    
    async def cleanup_all_sessions(self):
        """Clean up all active sessions"""
        for source in list(self.active_sessions.keys()):
            await self._cleanup_session(source)

class BatchProcessor:
    """
    Process multiple properties efficiently using session management
    """
    
    def __init__(self):
        self.session_manager = SessionManagedScraper()
        
    async def process_property_batch(self, properties: List[Dict[str, Any]], 
                                   source: str = "batchleads") -> List[Dict[str, Any]]:
        """
        Process a batch of properties efficiently
        """
        results = []
        
        try:
            for i, property_data in enumerate(properties):
                logger.info(f"Processing property {i+1}/{len(properties)}: {property_data.get('address')}")
                
                result = await self.session_manager.get_comps_with_session(property_data, source)
                results.append({
                    "property": property_data,
                    "comps_result": result
                })
                
                # Add delay between requests to be respectful
                if i < len(properties) - 1:
                    await asyncio.sleep(2)
                    
        except Exception as e:
            logger.error(f"Batch processing failed: {str(e)}")
        
        finally:
            # Clean up sessions
            await self.session_manager.cleanup_all_sessions()
        
        return results

# Utility functions for easy integration
async def get_comps_efficiently(property_data: Dict[str, Any], 
                              source: str = "batchleads") -> Dict[str, Any]:
    """
    Get comps efficiently using session management
    """
    session_manager = SessionManagedScraper()
    try:
        return await session_manager.get_comps_with_session(property_data, source)
    finally:
        await session_manager.cleanup_all_sessions()

async def process_multiple_properties(properties: List[Dict[str, Any]], 
                                    source: str = "batchleads") -> List[Dict[str, Any]]:
    """
    Process multiple properties in batch
    """
    processor = BatchProcessor()
    return await processor.process_property_batch(properties, source)
