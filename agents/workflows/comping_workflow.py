
import os
import logging
import json
import time
import random
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from knowledge_pipeline.utils.query_kb import query_kb
from agents.workflows.comp_aggregator import CompAggregator
from agents.workflows.smart_comping_orchestrator import get_smart_comps
from agents.workflows.multi_tier_comping_orchestrator import get_multi_tier_comps
from scrapers.scraper_factory import get_comps_from_source

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompingWorkflow:
    """
    Orchestrates the workflow for obtaining and aggregating property comps
    from BatchLeads as the primary source with minimal fallback for MVP.
    """

    def __init__(self):
        """Initialize the comping workflow orchestrator."""
        self.aggregator = CompAggregator()
        # For MVP, we'll use a simplified configuration with BatchLeads as primary
        self.sources_config = self._get_comping_sources_config()

    def _get_comping_sources_config(self) -> Dict[str, Dict[str, Any]]:
        """
        Get a simplified configuration for comping sources with BatchLeads as primary.

        Returns:
            Dict[str, Dict[str, Any]]: Configuration for each comping source
        """
        # For MVP, we'll use a hardcoded configuration with BatchLeads as primary
        return {
            "batchleads": {
                "priority": 1,
                "enabled": True,
                "api_key_env": "BATCHLEADS_API_KEY",
                "timeout": 30,
                "max_retries": 3,
                "weight": 0.8
            },
            "privy": {
                "priority": 2,
                "enabled": True,
                "api_key_env": "PRIVY_API_KEY",
                "timeout": 30,
                "max_retries": 2,
                "weight": 0.6
            }
        }

    def _get_business_rules(self) -> Dict[str, Any]:
        """
        Get simplified business rules for comping.

        Returns:
            Dict[str, Any]: Business rules for comping
        """
        # For MVP, we'll use hardcoded business rules
        return {
            "max_distance_miles": 1.0,
            "max_age_days": 180,
            "max_sqft_diff_percent": 20,
            "min_comps_required": 3,
            "preferred_comps_count": 5,
            "max_comps_to_use": 10,
            "adjust_for_age": True,
            "adjust_for_condition": True,
            "adjust_for_size": True,
            "size_adjustment_rate": 0.1,  # 10% per 100 sqft
            "age_adjustment_rate": 0.005  # 0.5% per year
        }

    def _check_api_key(self, source: str, config: Dict[str, Any]) -> bool:
        """
        Check if the API key for a source is available.

        Args:
            source (str): The name of the source
            config (Dict[str, Any]): The configuration for the source

        Returns:
            bool: True if the API key is available, False otherwise
        """
        if "api_key_env" not in config:
            return True

        api_key = os.environ.get(config["api_key_env"])
        if not api_key:
            logger.warning(f"API key for {source} not found in environment variables")
            return False

        return True

    def _get_prioritized_sources(self) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Get sources prioritized by their priority and availability.

        Returns:
            List[Tuple[str, Dict[str, Any]]]: List of (source_name, config) tuples
        """
        # Filter enabled sources
        enabled_sources = [
            (source, config) for source, config in self.sources_config.items()
            if config.get("enabled", False)
        ]

        # Check API keys
        available_sources = [
            (source, config) for source, config in enabled_sources
            if self._check_api_key(source, config)
        ]

        # Sort by priority
        prioritized_sources = sorted(
            available_sources,
            key=lambda x: x[1].get("priority", 999)
        )

        if not prioritized_sources:
            logger.warning("No comping sources available")

        return prioritized_sources

    async def get_comps_for_property(self, property_data: Dict[str, Any], parallel: bool = False) -> Dict[str, Any]:
        """
        Get comps for a property using BatchLeads as primary source with minimal fallback.

        Args:
            property_data (Dict[str, Any]): The property data
            parallel (bool, optional): Whether to get comps from multiple sources in parallel. Defaults to False.

        Returns:
            Dict[str, Any]: The aggregated and normalized comps data
        """
        logger.info(f"Getting comps for property at {property_data.get('address')}")

        # Get prioritized sources
        prioritized_sources = self._get_prioritized_sources()

        # Get business rules
        business_rules = self._get_business_rules()

        # Placeholder for results from all sources
        all_source_results = []

        # For MVP, we'll use sequential execution and focus on BatchLeads
        for source, config in prioritized_sources:
            try:
                logger.info(f"Getting comps from {source}")

                # Use the scraper factory to get comps
                result = await get_comps_from_source(property_data, source)

                # Add source weight from config
                if result.get("success", False):
                    result["weight"] = config.get("weight", 0.5)
                    all_source_results.append(result)
                    logger.info(f"Received {len(result.get('comps', []))} comps from {source}")

                    # For MVP, if we get enough comps from BatchLeads, we can stop
                    if source == "batchleads" and len(result.get("comps", [])) >= business_rules.get("min_comps_required", 3):
                        logger.info(f"Got sufficient comps from BatchLeads, skipping other sources for MVP")
                        break
                else:
                    logger.warning(f"Failed to get comps from {source}: {result.get('error', 'Unknown error')}")
            except Exception as e:
                logger.error(f"Error getting comps from {source}: {str(e)}")

        # If we have no results, try fallback to Privy if it wasn't already tried
        if not all_source_results and "privy" in self.sources_config:
            try:
                logger.info("No comps found, trying fallback to Privy")
                result = await get_comps_from_source(property_data, "privy")

                if result.get("success", False):
                    result["weight"] = self.sources_config["privy"].get("weight", 0.5)
                    all_source_results.append(result)
                    logger.info(f"Received {len(result.get('comps', []))} comps from Privy fallback")
            except Exception as e:
                logger.error(f"Error getting comps from Privy fallback: {str(e)}")

        # Aggregate and normalize the results
        aggregated_results = self.aggregator.aggregate_comps(
            all_source_results,
            property_data,
            business_rules
        )

        # Check if we have enough comps
        min_comps_required = business_rules.get("min_comps_required", 3)
        if len(aggregated_results.get("comps", [])) < min_comps_required:
            logger.warning(f"Not enough comps found ({len(aggregated_results.get('comps', []))}/{min_comps_required})")
            aggregated_results["warning"] = f"Not enough comps found ({len(aggregated_results.get('comps', []))}/{min_comps_required})"

        return aggregated_results

# Multi-tier robust comping workflow (RECOMMENDED)
async def run_multi_tier_comping_workflow(property_data: Dict[str, Any], strategy: str = "balanced") -> Dict[str, Any]:
    """
    Run the multi-tier robust comping workflow using Realie.ai + RentCast + scraping fallback.

    Args:
        property_data (Dict[str, Any]): The property data
        strategy (str): Comping strategy ("premium", "balanced", "cost_optimized", "speed_first")

    Returns:
        Dict[str, Any]: The comps data with multi-tier processing
    """
    logger.info(f"Running multi-tier comping workflow for {property_data.get('address')} with {strategy} strategy")

    try:
        # Use multi-tier orchestrator
        result = await get_multi_tier_comps(property_data, strategy)

        if result.get('success'):
            logger.info(f"Multi-tier comping successful: {len(result.get('comps', []))} comps found")
            return result
        else:
            logger.warning(f"Multi-tier comping failed, falling back to enhanced workflow")
            # Fallback to enhanced workflow
            return await run_enhanced_comping_workflow(property_data, "cost_optimized")

    except Exception as e:
        logger.error(f"Multi-tier comping workflow error: {str(e)}")
        # Fallback to enhanced workflow
        return await run_enhanced_comping_workflow(property_data, "cost_optimized")

# Enhanced helper function using smart orchestrator
async def run_enhanced_comping_workflow(property_data: Dict[str, Any], strategy: str = "cost_optimized") -> Dict[str, Any]:
    """
    Run the enhanced comping workflow using smart orchestration.

    Args:
        property_data (Dict[str, Any]): The property data
        strategy (str): Comping strategy ("speed_first", "cost_optimized", "comprehensive", "scraping_only")

    Returns:
        Dict[str, Any]: The comps data with enhanced processing
    """
    logger.info(f"Running enhanced comping workflow for {property_data.get('address')} with {strategy} strategy")

    try:
        # Use smart orchestrator
        result = await get_smart_comps(property_data, strategy)

        if result.get('success'):
            logger.info(f"Enhanced comping successful: {len(result.get('comps', []))} comps found")
            return result
        else:
            logger.warning(f"Enhanced comping failed, falling back to traditional workflow")
            # Fallback to traditional workflow
            return await run_comping_workflow(property_data)

    except Exception as e:
        logger.error(f"Enhanced comping workflow error: {str(e)}")
        # Fallback to traditional workflow
        return await run_comping_workflow(property_data)

# Helper function to run the comping workflow for a property
async def run_comping_workflow(property_data: Dict[str, Any], parallel: bool = False) -> Dict[str, Any]:
    """
    Run the comping workflow for a property.

    Args:
        property_data (Dict[str, Any]): The property data
        parallel (bool, optional): Whether to get comps from multiple sources in parallel. Defaults to False.

    Returns:
        Dict[str, Any]: The aggregated and normalized comps data
    """
    workflow = CompingWorkflow()
    return await workflow.get_comps_for_property(property_data, parallel)

async def calculate_arv(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate the After Repair Value (ARV) for a property using the comping workflow.

    Args:
        property_data (Dict[str, Any]): The property data

    Returns:
        Dict[str, Any]: ARV calculation result
    """
    logger.info(f"Calculating ARV for property at {property_data.get('address')}")

    # Run the comping workflow
    comps_result = await run_comping_workflow(property_data)

    # Extract ARV from the result
    arv_result = comps_result.get("arv_result", {})

    # Create a simplified result
    result = {
        "success": comps_result.get("success", False) and arv_result.get("arv", 0) > 0,
        "property_id": property_data.get("id", ""),
        "property_address": property_data.get("address", ""),
        "arv": arv_result.get("arv", 0),
        "confidence": arv_result.get("confidence", 0),
        "comps_used": len(comps_result.get("comps", [])),
        "comp_count": comps_result.get("final_comp_count", 0),
        "warning": comps_result.get("warning", None)
    }

    if not result["success"]:
        result["error"] = "Failed to calculate ARV" if not comps_result.get("success", False) else "Invalid ARV value"

    return result

