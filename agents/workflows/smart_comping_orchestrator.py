"""
Smart Comping Orchestrator
Intelligently routes comping requests through the most efficient data sources
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import aiohttp

from agents.workflows.enhanced_comping_service import EnhancedCompingService
from agents.workflows.session_managed_scraper import SessionManagedScraper

logger = logging.getLogger(__name__)

class SmartCompingOrchestrator:
    """
    Orchestrates comping requests using the most efficient strategy:
    1. Check cache first
    2. Try fast API sources
    3. Use session-managed scraping as fallback
    4. Aggregate and normalize results
    """
    
    def __init__(self):
        self.enhanced_service = EnhancedCompingService()
        self.session_scraper = SessionManagedScraper()
        self.config = self._load_config()
        self.stats = {
            'api_calls': 0,
            'scraping_calls': 0,
            'cache_hits': 0,
            'total_cost': 0.0
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """Load API sources configuration"""
        try:
            config_path = 'config/api_sources_config.json'
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load API config: {str(e)}")
            return {}
    
    async def get_optimal_comps(self, property_data: Dict[str, Any], 
                              strategy: str = "cost_optimized") -> Dict[str, Any]:
        """
        Get comps using the optimal strategy based on requirements
        
        Strategies:
        - "speed_first": Prioritize speed over cost
        - "cost_optimized": Balance speed and cost
        - "comprehensive": Get maximum data regardless of cost
        - "scraping_only": Use only scraping (for testing)
        """
        
        start_time = datetime.now()
        address = property_data.get('address', 'Unknown')
        
        logger.info(f"Getting optimal comps for {address} using {strategy} strategy")
        
        try:
            if strategy == "speed_first":
                result = await self._speed_first_strategy(property_data)
            elif strategy == "cost_optimized":
                result = await self._cost_optimized_strategy(property_data)
            elif strategy == "comprehensive":
                result = await self._comprehensive_strategy(property_data)
            elif strategy == "scraping_only":
                result = await self._scraping_only_strategy(property_data)
            else:
                result = await self._cost_optimized_strategy(property_data)
            
            # Add performance metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            result['performance'] = {
                'processing_time_seconds': processing_time,
                'strategy_used': strategy,
                'stats': self.stats.copy()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Optimal comping failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'strategy_used': strategy
            }
    
    async def _speed_first_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Speed-first strategy: API only, no scraping"""
        
        # Try RealtyMole first (fastest API)
        result = await self._try_realty_mole_api(property_data)
        if result.get('success') and len(result.get('comps', [])) >= 3:
            return result
        
        # Try SmartyStreets for property validation + basic data
        smarty_result = await self._try_smarty_streets_api(property_data)
        if smarty_result.get('success'):
            # Combine results
            all_comps = result.get('comps', []) + smarty_result.get('comps', [])
            if len(all_comps) >= 2:
                return {
                    'success': True,
                    'comps': all_comps,
                    'source': 'api_combined_speed',
                    'strategy': 'speed_first'
                }
        
        # If still insufficient, return what we have
        return {
            'success': len(result.get('comps', [])) > 0,
            'comps': result.get('comps', []),
            'source': 'api_partial',
            'strategy': 'speed_first',
            'note': 'Insufficient comps found with speed-first strategy'
        }
    
    async def _cost_optimized_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Cost-optimized strategy: Try cheapest API first, then selective scraping"""
        
        # Try RealtyMole (cheapest comprehensive API)
        result = await self._try_realty_mole_api(property_data)
        
        if result.get('success') and len(result.get('comps', [])) >= 3:
            logger.info("Sufficient comps from RealtyMole API")
            return result
        
        # If insufficient, try one session-managed scraping call
        logger.info("API insufficient, trying session-managed scraping")
        scraping_result = await self.session_scraper.get_comps_with_session(
            property_data, "batchleads"
        )
        
        # Combine API and scraping results
        all_comps = result.get('comps', []) + scraping_result.get('comps', [])
        
        return {
            'success': len(all_comps) > 0,
            'comps': all_comps[:10],  # Limit to top 10
            'source': 'api_plus_scraping',
            'strategy': 'cost_optimized',
            'api_comps': len(result.get('comps', [])),
            'scraping_comps': len(scraping_result.get('comps', []))
        }
    
    async def _comprehensive_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive strategy: Use all available sources"""
        
        # Use the enhanced comping service which tries everything
        return await self.enhanced_service.get_comprehensive_comps(property_data)
    
    async def _scraping_only_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Scraping-only strategy: For testing or when APIs are unavailable"""
        
        return await self.session_scraper.get_comps_with_session(property_data, "batchleads")
    
    async def _try_realty_mole_api(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Try RealtyMole API"""
        try:
            api_key = os.getenv('REALTY_MOLE_API_KEY')
            if not api_key:
                return {'success': False, 'error': 'No RealtyMole API key'}
            
            address = f"{property_data.get('address')}, {property_data.get('city')}, {property_data.get('state')}"
            
            async with aiohttp.ClientSession() as session:
                url = "https://api.realtymole.com/v1/properties/comparables"
                headers = {'X-API-Key': api_key}
                params = {
                    'address': address,
                    'radius': '1',
                    'limit': '10'
                }
                
                async with session.get(url, headers=headers, params=params, timeout=15) as response:
                    if response.status == 200:
                        data = await response.json()
                        comps = self._normalize_realty_mole_data(data)
                        
                        self.stats['api_calls'] += 1
                        self.stats['total_cost'] += 0.029  # RealtyMole cost
                        
                        return {
                            'success': True,
                            'comps': comps,
                            'source': 'realty_mole_api',
                            'raw_data': data
                        }
                    else:
                        return {'success': False, 'error': f'API error: {response.status}'}
                        
        except Exception as e:
            logger.warning(f"RealtyMole API failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def _try_smarty_streets_api(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Try SmartyStreets API for property validation"""
        try:
            auth_id = os.getenv('SMARTY_STREETS_AUTH_ID')
            auth_token = os.getenv('SMARTY_STREETS_AUTH_TOKEN')
            
            if not auth_id or not auth_token:
                return {'success': False, 'error': 'No SmartyStreets credentials'}
            
            # SmartyStreets is mainly for validation, but provides some property data
            # This is a placeholder for actual implementation
            self.stats['api_calls'] += 1
            self.stats['total_cost'] += 0.30  # SmartyStreets cost
            
            return {'success': False, 'error': 'SmartyStreets integration not implemented yet'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _normalize_realty_mole_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Normalize RealtyMole API data"""
        comps = []
        
        for prop in data.get('properties', []):
            comp = {
                'address': prop.get('address'),
                'city': prop.get('city'),
                'state': prop.get('state'),
                'zip_code': prop.get('zipCode'),
                'sale_price': self._parse_price(prop.get('price')),
                'sale_date': prop.get('lastSaleDate'),
                'beds': self._parse_int(prop.get('bedrooms')),
                'baths': self._parse_float(prop.get('bathrooms')),
                'sqft': self._parse_int(prop.get('squareFootage')),
                'lot_size': self._parse_int(prop.get('lotSize')),
                'year_built': self._parse_int(prop.get('yearBuilt')),
                'property_type': prop.get('propertyType', '').lower(),
                'source': 'realty_mole_api',
                'confidence_score': 0.8  # RealtyMole generally has good data
            }
            comps.append(comp)
        
        return comps
    
    def _parse_price(self, price_str: Any) -> Optional[int]:
        """Parse price string to integer"""
        if not price_str:
            return None
        try:
            # Remove currency symbols and commas
            clean_price = str(price_str).replace('$', '').replace(',', '')
            return int(float(clean_price))
        except (ValueError, TypeError):
            return None
    
    def _parse_int(self, value: Any) -> Optional[int]:
        """Parse value to integer"""
        if not value:
            return None
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return None
    
    def _parse_float(self, value: Any) -> Optional[float]:
        """Parse value to float"""
        if not value:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    async def batch_process_properties(self, properties: List[Dict[str, Any]], 
                                     strategy: str = "cost_optimized") -> List[Dict[str, Any]]:
        """Process multiple properties efficiently"""
        results = []
        
        for i, property_data in enumerate(properties):
            logger.info(f"Processing property {i+1}/{len(properties)}")
            
            result = await self.get_optimal_comps(property_data, strategy)
            results.append({
                'property': property_data,
                'comps_result': result
            })
            
            # Rate limiting
            if i < len(properties) - 1:
                await asyncio.sleep(1)
        
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'stats': self.stats.copy(),
            'cost_per_property': self.stats['total_cost'] / max(1, self.stats['api_calls'] + self.stats['scraping_calls']),
            'api_success_rate': self.stats['api_calls'] / max(1, self.stats['api_calls'] + self.stats['scraping_calls']),
            'cache_hit_rate': self.stats['cache_hits'] / max(1, self.stats['api_calls'] + self.stats['scraping_calls'] + self.stats['cache_hits'])
        }

# Factory function for easy integration
async def get_smart_comps(property_data: Dict[str, Any], 
                         strategy: str = "cost_optimized") -> Dict[str, Any]:
    """
    Get comps using smart orchestration
    """
    orchestrator = SmartCompingOrchestrator()
    return await orchestrator.get_optimal_comps(property_data, strategy)
