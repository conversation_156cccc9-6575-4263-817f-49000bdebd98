"""
Multi-Tier Robust Comping Orchestrator
Combines multiple premium data sources for the most comprehensive property analysis
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import aiohttp

from agents.realie_agent import get_realie_comparables, get_realie_property_details
from agents.rentcast_agent import get_rentcast_comps, get_rentcast_value_estimate, get_rentcast_rent_estimate
from agents.workflows.session_managed_scraper import SessionManagedScraper

logger = logging.getLogger(__name__)

class MultiTierCompingOrchestrator:
    """
    Multi-tier comping orchestrator that uses the best available data sources:
    
    Tier 1: Realie.ai Premium (County-sourced, 100+ fields, premium comparables)
    Tier 2: RentCast API (Comprehensive property data, AVM, rent estimates)
    Tier 3: Session-managed scraping (BatchLeads, Privy, Lotside)
    
    Strategies:
    - "premium": Use all tiers for maximum data quality
    - "balanced": Use Tier 1 + Tier 2, fallback to Tier 3
    - "cost_optimized": Use Tier 2 first, then Tier 1 if needed
    - "speed_first": Use fastest available sources only
    """
    
    def __init__(self):
        self.session_scraper = SessionManagedScraper()
        self.stats = {
            'realie_calls': 0,
            'rentcast_calls': 0,
            'scraping_calls': 0,
            'cache_hits': 0,
            'total_cost': 0.0
        }
        self.cache = {}
        self.cache_duration = timedelta(hours=24)
    
    async def get_comprehensive_comps(self, property_data: Dict[str, Any], 
                                    strategy: str = "balanced") -> Dict[str, Any]:
        """
        Get comprehensive comps using multi-tier approach
        
        Args:
            property_data: Property information
            strategy: Comping strategy to use
            
        Returns:
            Dict containing comprehensive comps data
        """
        start_time = datetime.now()
        address = property_data.get('address', 'Unknown')
        
        logger.info(f"Getting comprehensive comps for {address} using {strategy} strategy")
        
        # Check cache first
        cache_key = self._generate_cache_key(property_data)
        if self._is_cached(cache_key):
            logger.info(f"Using cached comps for {address}")
            self.stats['cache_hits'] += 1
            return self.cache[cache_key]['data']
        
        try:
            if strategy == "premium":
                result = await self._premium_strategy(property_data)
            elif strategy == "balanced":
                result = await self._balanced_strategy(property_data)
            elif strategy == "cost_optimized":
                result = await self._cost_optimized_strategy(property_data)
            elif strategy == "speed_first":
                result = await self._speed_first_strategy(property_data)
            else:
                result = await self._balanced_strategy(property_data)
            
            # Add performance metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            result['performance'] = {
                'processing_time_seconds': processing_time,
                'strategy_used': strategy,
                'stats': self.stats.copy()
            }
            
            # Cache the results
            self._cache_results(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive comping failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'strategy_used': strategy
            }
    
    async def _premium_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Premium strategy: Use all available sources for maximum data quality"""
        
        logger.info("Using premium strategy - all data sources")
        
        # Tier 1: Realie.ai Premium Comparables
        realie_result = await self._get_realie_data(property_data)
        
        # Tier 2: RentCast comprehensive data
        rentcast_result = await self._get_rentcast_data(property_data)
        
        # Tier 3: Scraping for additional coverage (if needed)
        scraping_result = {}
        total_comps = len(realie_result.get('comps', [])) + len(rentcast_result.get('comps', []))
        
        if total_comps < 5:  # If we need more comps
            logger.info("Getting additional comps from scraping")
            scraping_result = await self.session_scraper.get_comps_with_session(
                property_data, "batchleads"
            )
            self.stats['scraping_calls'] += 1
        
        # Combine all results
        return self._combine_multi_tier_results(
            realie_result, rentcast_result, scraping_result, property_data, "premium"
        )
    
    async def _balanced_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Balanced strategy: Use Tier 1 + Tier 2, fallback to Tier 3"""
        
        logger.info("Using balanced strategy - premium APIs with scraping fallback")
        
        # Try Realie.ai first (highest quality)
        realie_result = await self._get_realie_data(property_data)
        
        # If Realie gives us good results, supplement with RentCast
        if realie_result.get('success') and len(realie_result.get('comps', [])) >= 3:
            logger.info("Good results from Realie, supplementing with RentCast")
            rentcast_result = await self._get_rentcast_data(property_data)
            
            return self._combine_multi_tier_results(
                realie_result, rentcast_result, {}, property_data, "balanced"
            )
        
        # If Realie insufficient, try RentCast
        logger.info("Realie insufficient, trying RentCast")
        rentcast_result = await self._get_rentcast_data(property_data)
        
        # If still insufficient, use scraping
        total_comps = len(realie_result.get('comps', [])) + len(rentcast_result.get('comps', []))
        scraping_result = {}
        
        if total_comps < 3:
            logger.info("APIs insufficient, using scraping fallback")
            scraping_result = await self.session_scraper.get_comps_with_session(
                property_data, "batchleads"
            )
            self.stats['scraping_calls'] += 1
        
        return self._combine_multi_tier_results(
            realie_result, rentcast_result, scraping_result, property_data, "balanced"
        )
    
    async def _cost_optimized_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Cost-optimized strategy: Use RentCast first, then Realie if needed"""
        
        logger.info("Using cost-optimized strategy - RentCast first")
        
        # Try RentCast first (lower cost)
        rentcast_result = await self._get_rentcast_data(property_data)
        
        if rentcast_result.get('success') and len(rentcast_result.get('comps', [])) >= 3:
            logger.info("Sufficient comps from RentCast")
            return self._combine_multi_tier_results(
                {}, rentcast_result, {}, property_data, "cost_optimized"
            )
        
        # If insufficient, upgrade to Realie
        logger.info("RentCast insufficient, upgrading to Realie")
        realie_result = await self._get_realie_data(property_data)
        
        return self._combine_multi_tier_results(
            realie_result, rentcast_result, {}, property_data, "cost_optimized"
        )
    
    async def _speed_first_strategy(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Speed-first strategy: Use fastest APIs only"""
        
        logger.info("Using speed-first strategy - fastest APIs only")
        
        # Run RentCast and Realie in parallel for speed
        realie_task = asyncio.create_task(self._get_realie_data(property_data))
        rentcast_task = asyncio.create_task(self._get_rentcast_data(property_data))
        
        # Wait for both to complete
        realie_result, rentcast_result = await asyncio.gather(
            realie_task, rentcast_task, return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(realie_result, Exception):
            logger.warning(f"Realie failed in parallel: {str(realie_result)}")
            realie_result = {'success': False, 'comps': []}
        
        if isinstance(rentcast_result, Exception):
            logger.warning(f"RentCast failed in parallel: {str(rentcast_result)}")
            rentcast_result = {'success': False, 'comps': []}
        
        return self._combine_multi_tier_results(
            realie_result, rentcast_result, {}, property_data, "speed_first"
        )
    
    async def _get_realie_data(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get data from Realie.ai"""
        try:
            # Get premium comparables
            comps_result = await get_realie_comparables(
                property_data, radius_miles=1.0, time_frame_months=18, max_results=10
            )
            
            # Get detailed property info
            property_result = await get_realie_property_details(property_data)
            
            self.stats['realie_calls'] += 2  # Two API calls
            self.stats['total_cost'] += 0.10  # Estimated cost for 2 calls
            
            if comps_result.get('success'):
                return {
                    'success': True,
                    'comps': comps_result.get('comps', []),
                    'property_details': property_result.get('property', {}),
                    'source': 'realie_premium',
                    'total_found': comps_result.get('total_found', 0)
                }
            else:
                return comps_result
                
        except Exception as e:
            logger.error(f"Realie data retrieval failed: {str(e)}")
            return {'success': False, 'error': str(e), 'comps': []}
    
    async def _get_rentcast_data(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get data from RentCast"""
        try:
            # Get comparables and value estimate in parallel
            comps_task = asyncio.create_task(get_rentcast_comps(property_data, radius_miles=1.0, limit=10))
            value_task = asyncio.create_task(get_rentcast_value_estimate(property_data))
            rent_task = asyncio.create_task(get_rentcast_rent_estimate(property_data))
            
            comps_result, value_result, rent_result = await asyncio.gather(
                comps_task, value_task, rent_task, return_exceptions=True
            )
            
            self.stats['rentcast_calls'] += 3  # Three API calls
            self.stats['total_cost'] += 0.30  # Estimated cost for 3 calls
            
            # Handle exceptions
            if isinstance(comps_result, Exception):
                comps_result = {'success': False, 'comps': []}
            if isinstance(value_result, Exception):
                value_result = {'success': False}
            if isinstance(rent_result, Exception):
                rent_result = {'success': False}
            
            return {
                'success': comps_result.get('success', False),
                'comps': comps_result.get('comps', []),
                'value_estimate': value_result.get('value_estimate') if value_result.get('success') else None,
                'rent_estimate': rent_result.get('rent_estimate') if rent_result.get('success') else None,
                'source': 'rentcast_comprehensive',
                'total_found': len(comps_result.get('comps', []))
            }
            
        except Exception as e:
            logger.error(f"RentCast data retrieval failed: {str(e)}")
            return {'success': False, 'error': str(e), 'comps': []}
    
    def _combine_multi_tier_results(self, realie_result: Dict[str, Any], 
                                  rentcast_result: Dict[str, Any],
                                  scraping_result: Dict[str, Any],
                                  property_data: Dict[str, Any],
                                  strategy: str) -> Dict[str, Any]:
        """Combine results from multiple tiers"""
        
        all_comps = []
        sources_used = []
        
        # Add Realie comps (highest priority)
        if realie_result.get('comps'):
            all_comps.extend(realie_result['comps'])
            sources_used.append('realie_premium')
        
        # Add RentCast comps
        if rentcast_result.get('comps'):
            all_comps.extend(rentcast_result['comps'])
            sources_used.append('rentcast_api')
        
        # Add scraping comps
        if scraping_result.get('comps'):
            all_comps.extend(scraping_result['comps'])
            sources_used.append('scraping_fallback')
        
        # Remove duplicates and sort by quality
        unique_comps = self._deduplicate_and_rank_comps(all_comps, property_data)
        
        # Calculate comprehensive metrics
        arv_estimate = self._calculate_arv_from_multi_tier(unique_comps)
        confidence_score = self._calculate_confidence_score(unique_comps, sources_used)
        
        return {
            'success': len(unique_comps) > 0,
            'comps': unique_comps[:10],  # Top 10 comps
            'total_comps_found': len(all_comps),
            'unique_comps': len(unique_comps),
            'sources_used': sources_used,
            'strategy': strategy,
            'arv_estimate': arv_estimate,
            'confidence_score': confidence_score,
            'value_estimate': rentcast_result.get('value_estimate'),
            'rent_estimate': rentcast_result.get('rent_estimate'),
            'property_details': realie_result.get('property_details', {}),
            'timestamp': datetime.now().isoformat()
        }
    
    def _deduplicate_and_rank_comps(self, comps: List[Dict[str, Any]], 
                                  property_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Remove duplicates and rank comps by quality and relevance"""
        
        # Remove duplicates based on address
        seen_addresses = set()
        unique_comps = []
        
        for comp in comps:
            address_key = f"{comp.get('address', '')}-{comp.get('city', '')}"
            if address_key not in seen_addresses:
                seen_addresses.add(address_key)
                unique_comps.append(comp)
        
        # Rank by quality score
        for comp in unique_comps:
            comp['quality_score'] = self._calculate_comp_quality_score(comp, property_data)
        
        # Sort by quality score (highest first)
        return sorted(unique_comps, key=lambda x: x.get('quality_score', 0), reverse=True)
    
    def _calculate_comp_quality_score(self, comp: Dict[str, Any], property_data: Dict[str, Any]) -> float:
        """Calculate quality score for a comp"""
        score = 0.0
        
        # Source quality weight
        source_weights = {
            'realie_premium_api': 1.0,
            'rentcast_api': 0.8,
            'scraping_fallback': 0.6
        }
        score += source_weights.get(comp.get('source', ''), 0.5)
        
        # Recency weight (more recent = better)
        if comp.get('sale_date'):
            try:
                sale_date = datetime.fromisoformat(comp['sale_date'].replace('Z', '+00:00'))
                days_old = (datetime.now() - sale_date).days
                recency_score = max(0, 1 - (days_old / 365))  # Decay over 1 year
                score += recency_score * 0.3
            except:
                pass
        
        # Size similarity weight
        if property_data.get('sqft') and comp.get('sqft'):
            size_diff = abs(comp['sqft'] - property_data['sqft']) / property_data['sqft']
            size_score = max(0, 1 - size_diff)
            score += size_score * 0.2
        
        return score
    
    def _calculate_arv_from_multi_tier(self, comps: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate ARV using weighted average from multi-tier comps"""
        if not comps:
            return None
        
        weighted_prices = []
        total_weight = 0
        
        for comp in comps:
            if comp.get('sale_price') and comp.get('quality_score'):
                weight = comp['quality_score']
                weighted_prices.append(comp['sale_price'] * weight)
                total_weight += weight
        
        if total_weight > 0:
            return sum(weighted_prices) / total_weight
        
        return None
    
    def _calculate_confidence_score(self, comps: List[Dict[str, Any]], sources_used: List[str]) -> float:
        """Calculate confidence score based on data quality and sources"""
        if not comps:
            return 0.0
        
        # Base confidence from number of comps
        comp_confidence = min(len(comps) / 5, 1.0)  # Max confidence at 5+ comps
        
        # Source quality bonus
        source_bonus = 0.0
        if 'realie_premium' in sources_used:
            source_bonus += 0.3
        if 'rentcast_api' in sources_used:
            source_bonus += 0.2
        
        # Average quality score of comps
        avg_quality = sum(comp.get('quality_score', 0) for comp in comps) / len(comps)
        
        return min((comp_confidence + source_bonus + avg_quality) / 3, 1.0)
    
    def _generate_cache_key(self, property_data: Dict[str, Any]) -> str:
        """Generate cache key for property"""
        address = property_data.get('address', '')
        city = property_data.get('city', '')
        state = property_data.get('state', '')
        return f"{address}-{city}-{state}".lower().replace(' ', '-')
    
    def _is_cached(self, cache_key: str) -> bool:
        """Check if results are cached and still valid"""
        if cache_key not in self.cache:
            return False
        
        cached_time = self.cache[cache_key]['timestamp']
        return datetime.now() - cached_time < self.cache_duration
    
    def _cache_results(self, cache_key: str, results: Dict[str, Any]):
        """Cache results"""
        self.cache[cache_key] = {
            'data': results,
            'timestamp': datetime.now()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        total_calls = self.stats['realie_calls'] + self.stats['rentcast_calls'] + self.stats['scraping_calls']
        
        return {
            'stats': self.stats.copy(),
            'cost_per_property': self.stats['total_cost'] / max(1, total_calls),
            'api_success_rate': (self.stats['realie_calls'] + self.stats['rentcast_calls']) / max(1, total_calls),
            'cache_hit_rate': self.stats['cache_hits'] / max(1, total_calls + self.stats['cache_hits'])
        }

# Factory function for easy integration
async def get_multi_tier_comps(property_data: Dict[str, Any], 
                             strategy: str = "balanced") -> Dict[str, Any]:
    """
    Get comps using multi-tier orchestration
    
    Args:
        property_data: Property information
        strategy: "premium", "balanced", "cost_optimized", or "speed_first"
        
    Returns:
        Dict containing comprehensive comps data
    """
    orchestrator = MultiTierCompingOrchestrator()
    return await orchestrator.get_comprehensive_comps(property_data, strategy)
