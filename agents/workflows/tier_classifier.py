import logging
import json
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_tier_criteria():
    """
    Get the criteria for all lead tiers from the knowledge pipeline

    Returns:
        dict: The criteria for all lead tiers (1, 2, 3)
    """
    try:
        # Query the knowledge pipeline for tier criteria
        criteria_context = query_kb("What are the criteria for classifying leads as Tier 1, Tier 2, and Tier 3?")
        logger.info("Retrieved tier criteria from knowledge base")

        # Parse the context into structured criteria
        # This is a simplified example - in reality, we might need more sophisticated parsing
        criteria = {
            "tier_1": {},
            "tier_2": {},
            "tier_3": {}
        }

        # If the context contains JSON, try to parse it
        try:
            # Look for JSON-like content in the context
            start_idx = criteria_context.find('{')
            end_idx = criteria_context.rfind('}')

            if start_idx >= 0 and end_idx > start_idx:
                json_str = criteria_context[start_idx:end_idx+1]
                criteria = json.loads(json_str)
            else:
                # If no JSON found, use the whole context as a text rule
                criteria = {"rule": criteria_context}
        except json.JSONDecodeError:
            # If parsing fails, use the whole context as a text rule
            criteria = {"rule": criteria_context}

        return criteria

    except Exception as e:
        logger.error(f"Error getting tier criteria: {str(e)}")
        # Return default criteria as fallback
        return {
            "tier_1": {
                "min_property_value": 150000,
                "max_property_value": 500000,
                "min_equity": 50000,
                "motivated_seller_indicators": [
                    "behind on payments",
                    "foreclosure",
                    "divorce",
                    "relocation",
                    "inherited property",
                    "tax liens",
                    "tired landlord",
                    "code violations"
                ],
                "required_tags": ["tier_1"],
                "positive_response_indicators": [
                    "property address",
                    "address provided",
                    "interested in selling",
                    "off-market",
                    "exclusive listing"
                ]
            },
            "tier_2": {
                "description": "Leads where we made an offer but deal didn't work/get accepted",
                "required_conditions": [
                    "offer_made",
                    "deal_not_accepted"
                ],
                "follow_up_frequency_days": 14
            },
            "tier_3": {
                "description": "Do Not Contact - agents who responded negatively",
                "dnc_indicators": [
                    "do not contact",
                    "dnc",
                    "not interested",
                    "remove from list",
                    "stop contacting",
                    "unsubscribe",
                    "opt out",
                    "don't call",
                    "don't email",
                    "don't text"
                ]
            }
        }

def classify_lead_tier(lead_data):
    """
    Determine the tier classification (1, 2, or 3) for a lead based on criteria

    Args:
        lead_data (dict): The lead data from GHL

    Returns:
        int: The tier classification (1, 2, or 3)
    """
    try:
        # Get all tier criteria
        criteria = get_tier_criteria()

        # Extract relevant fields from lead data
        property_value = lead_data.get("property_value", 0)
        if isinstance(property_value, str):
            try:
                property_value = float(property_value.replace('$', '').replace(',', ''))
            except ValueError:
                property_value = 0

        equity = lead_data.get("equity", 0)
        if isinstance(equity, str):
            try:
                equity = float(equity.replace('$', '').replace(',', ''))
            except ValueError:
                equity = 0

        # Combine various text fields for analysis
        notes = lead_data.get("notes", "").lower()
        situation = lead_data.get("situation", "").lower()
        motivation = lead_data.get("motivation", "").lower()
        comments = lead_data.get("comments", "").lower()
        tags = lead_data.get("tags", "").lower()
        status = lead_data.get("status", "").lower()

        combined_text = f"{notes} {situation} {motivation} {comments} {tags} {status}"

        # Check for Tier 3 (DNC) first - highest priority
        tier_3_criteria = criteria.get("tier_3", {})
        dnc_indicators = tier_3_criteria.get("dnc_indicators", [])

        for indicator in dnc_indicators:
            if indicator in combined_text:
                logger.info(f"Lead classified as Tier 3 (DNC) due to indicator: {indicator}")
                return 3

        # Check for explicit DNC flag
        if lead_data.get("dnc", False) or "dnc" in tags:
            logger.info("Lead classified as Tier 3 (DNC) due to explicit DNC flag")
            return 3

        # Check for Tier 2 conditions
        tier_2_criteria = criteria.get("tier_2", {})
        if (lead_data.get("offer_made", False) or "offer made" in combined_text or
            lead_data.get("deal_status") == "not_accepted"):
            logger.info("Lead classified as Tier 2 - offer made but deal didn't work")
            return 2

        # Check for Tier 1 criteria
        tier_1_criteria = criteria.get("tier_1", {})
        reasons_not_tier1 = []

        # Check if agent responded positively with property address (key Tier 1 indicator)
        positive_indicators = tier_1_criteria.get("positive_response_indicators", [])
        found_positive_indicators = []
        for indicator in positive_indicators:
            if indicator in combined_text:
                found_positive_indicators.append(indicator)

        if not found_positive_indicators:
            reasons_not_tier1.append("No positive response indicators found (no property address provided)")

        # Check for required tags (like tier_1 tag from Gina)
        required_tags = tier_1_criteria.get("required_tags", [])
        for required_tag in required_tags:
            if required_tag not in tags and required_tag not in combined_text:
                reasons_not_tier1.append(f"Missing required tag: {required_tag}")

        # Property value check (if provided)
        if property_value > 0:
            min_value = tier_1_criteria.get("min_property_value", 0)
            max_value = tier_1_criteria.get("max_property_value", float('inf'))

            if property_value < min_value:
                reasons_not_tier1.append(f"Property value too low: ${property_value} < ${min_value}")

            if property_value > max_value:
                reasons_not_tier1.append(f"Property value too high: ${property_value} > ${max_value}")

        # Equity check (if provided)
        if equity > 0:
            min_equity = tier_1_criteria.get("min_equity", 0)
            if equity < min_equity:
                reasons_not_tier1.append(f"Equity too low: ${equity} < ${min_equity}")

        # Motivated seller indicators
        motivated_indicators = tier_1_criteria.get("motivated_seller_indicators", [])
        found_motivation_indicators = []
        for indicator in motivated_indicators:
            if indicator in combined_text:
                found_motivation_indicators.append(indicator)

        # If we have any reasons not to classify as Tier 1, it's Tier 2 by default
        if reasons_not_tier1:
            logger.info(f"Lead classified as Tier 2 for reasons: {'; '.join(reasons_not_tier1)}")
            return 2

        # If we've passed all checks, it's a Tier 1 lead
        logger.info(f"Lead classified as Tier 1 with positive indicators: {', '.join(found_positive_indicators)}")
        if found_motivation_indicators:
            logger.info(f"Found motivation indicators: {', '.join(found_motivation_indicators)}")
        return 1

    except Exception as e:
        logger.error(f"Error classifying lead tier: {str(e)}")
        # Default to Tier 2 if there's an error
        return 2

def batch_classify_leads(leads_data):
    """
    Classify multiple leads at once

    Args:
        leads_data (list): List of lead data dictionaries

    Returns:
        dict: Dictionary mapping lead IDs to tier classifications
    """
    results = {}

    for lead in leads_data:
        lead_id = lead.get("id")
        if not lead_id:
            continue

        tier = classify_lead_tier(lead)
        results[lead_id] = tier

    return results

def is_hot_lead(lead_data):
    """
    Determine if a lead is a "hot lead" requiring immediate attention

    Args:
        lead_data (dict): The lead data from GHL

    Returns:
        bool: True if this is a hot lead requiring immediate action
    """
    try:
        # Get tier classification
        tier = classify_lead_tier(lead_data)

        # Only Tier 1 leads can be hot leads
        if tier != 1:
            return False

        # Check for hot lead indicators
        notes = lead_data.get("notes", "").lower()
        situation = lead_data.get("situation", "").lower()
        motivation = lead_data.get("motivation", "").lower()
        comments = lead_data.get("comments", "").lower()
        tags = lead_data.get("tags", "").lower()

        combined_text = f"{notes} {situation} {motivation} {comments} {tags}"

        hot_indicators = [
            "urgent",
            "asap",
            "immediate",
            "quick sale",
            "need to sell fast",
            "foreclosure",
            "behind on payments",
            "divorce",
            "job loss",
            "medical emergency",
            "off-market",
            "exclusive",
            "motivated seller",
            "distressed",
            "cash needed"
        ]

        for indicator in hot_indicators:
            if indicator in combined_text:
                logger.info(f"Hot lead detected due to indicator: {indicator}")
                return True

        # Check if property address was provided (strong positive signal)
        address_indicators = [
            "property address",
            "address:",
            "located at",
            "property at"
        ]

        for indicator in address_indicators:
            if indicator in combined_text:
                logger.info(f"Hot lead detected - property address provided")
                return True

        return False

    except Exception as e:
        logger.error(f"Error determining if lead is hot: {str(e)}")
        return False

def get_tier_description(tier):
    """
    Get a human-readable description of the tier

    Args:
        tier (int): The tier number (1, 2, or 3)

    Returns:
        str: Description of the tier
    """
    descriptions = {
        1: "Tier 1 - Agent responded positively with property address",
        2: "Tier 2 - Offer made but deal didn't work/get accepted",
        3: "Tier 3 - Do Not Contact (DNC)"
    }
    return descriptions.get(tier, f"Unknown tier: {tier}")

def should_move_to_tier_2(lead_data):
    """
    Determine if a Tier 1 lead should be moved to Tier 2

    Args:
        lead_data (dict): The lead data

    Returns:
        bool: True if lead should be moved to Tier 2
    """
    # Check if an offer was made and not accepted
    offer_made = lead_data.get("offer_made", False)
    deal_status = lead_data.get("deal_status", "").lower()

    if offer_made and deal_status in ["rejected", "not_accepted", "declined", "no_response"]:
        return True

    # Check notes for offer rejection indicators
    notes = lead_data.get("notes", "").lower()
    comments = lead_data.get("comments", "").lower()
    combined_text = f"{notes} {comments}"

    rejection_indicators = [
        "offer rejected",
        "offer declined",
        "not interested in offer",
        "price too low",
        "deal fell through",
        "no longer interested"
    ]

    for indicator in rejection_indicators:
        if indicator in combined_text:
            return True

    return False
