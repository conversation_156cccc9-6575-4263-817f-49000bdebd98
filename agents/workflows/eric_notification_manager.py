"""
Eric Notification Manager

Handles internal notifications to <PERSON> for critical lead events,
call requests, and system alerts.
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from agents.ghl_client import GHLClient
from agents.workflows.tier_classifier import is_hot_lead, get_tier_description
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """Types of notifications to <PERSON>"""
    HOT_LEAD_ALERT = "hot_lead_alert"
    CALL_REQUEST = "call_request"
    SYSTEM_ALERT = "system_alert"
    DEAL_UPDATE = "deal_update"
    AGENT_RESPONSE = "agent_response"

class NotificationPriority(Enum):
    """Notification priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class EricNotification:
    """Notification to <PERSON>"""
    notification_type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    lead_id: Optional[str] = None
    agent_info: Optional[Dict[str, Any]] = None
    action_required: bool = False
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class EricNotificationManager:
    """
    Manages notifications to Eric for critical lead events and system alerts
    """
    
    def __init__(self, eric_contact_id: str = None):
        """
        Initialize the Eric notification manager
        
        Args:
            eric_contact_id (str): Eric's GHL contact ID for notifications
        """
        self.ghl_client = GHLClient()
        self.eric_contact_id = eric_contact_id or self._get_eric_contact_id()
        
        logger.info(f"Initialized Eric notification manager with contact ID: {self.eric_contact_id}")
    
    def _get_eric_contact_id(self) -> str:
        """Get Eric's contact ID from configuration or environment"""
        # This should be configured in your environment or config file
        # For now, return a placeholder - you'll need to set this up
        return "ERIC_CONTACT_ID_PLACEHOLDER"
    
    def notify_hot_lead(self, lead_id: str, lead_data: Dict[str, Any], 
                       actions_taken: List[str] = None) -> bool:
        """
        Notify Eric about a hot lead requiring immediate attention
        
        Args:
            lead_id (str): The ID of the hot lead
            lead_data (Dict[str, Any]): The lead data
            actions_taken (List[str]): List of actions already taken
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            # Extract agent information
            agent_info = {
                "first_name": lead_data.get("first_name", ""),
                "last_name": lead_data.get("last_name", ""),
                "phone": lead_data.get("phone", ""),
                "email": lead_data.get("email", ""),
                "property_address": lead_data.get("property_address", "")
            }
            
            # Determine hot lead indicators
            hot_indicators = self._identify_hot_indicators(lead_data)
            
            # Create notification message
            message = self._format_hot_lead_message(
                agent_info, hot_indicators, actions_taken or []
            )
            
            # Create notification
            notification = EricNotification(
                notification_type=NotificationType.HOT_LEAD_ALERT,
                priority=NotificationPriority.CRITICAL,
                title=f"🚨 HOT LEAD: {agent_info['first_name']} {agent_info['last_name']}",
                message=message,
                lead_id=lead_id,
                agent_info=agent_info,
                action_required=True
            )
            
            return self._send_notification(notification)
            
        except Exception as e:
            logger.error(f"Error notifying Eric about hot lead {lead_id}: {str(e)}")
            return False
    
    def request_call(self, lead_id: str, lead_data: Dict[str, Any], 
                    call_reason: str, priority: NotificationPriority = NotificationPriority.HIGH) -> bool:
        """
        Request Eric to call an agent
        
        Args:
            lead_id (str): The ID of the lead
            lead_data (Dict[str, Any]): The lead data
            call_reason (str): Reason for the call request
            priority (NotificationPriority): Priority of the call request
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            # Extract agent information
            agent_info = {
                "first_name": lead_data.get("first_name", ""),
                "last_name": lead_data.get("last_name", ""),
                "phone": lead_data.get("phone", ""),
                "email": lead_data.get("email", ""),
                "property_address": lead_data.get("property_address", ""),
                "company": lead_data.get("company", ""),
                "lead_source": lead_data.get("lead_source", "")
            }
            
            # Create call request message
            message = self._format_call_request_message(agent_info, call_reason, lead_data)
            
            # Create notification
            notification = EricNotification(
                notification_type=NotificationType.CALL_REQUEST,
                priority=priority,
                title=f"📞 CALL REQUEST: {agent_info['first_name']} {agent_info['last_name']}",
                message=message,
                lead_id=lead_id,
                agent_info=agent_info,
                action_required=True
            )
            
            return self._send_notification(notification)
            
        except Exception as e:
            logger.error(f"Error requesting call for lead {lead_id}: {str(e)}")
            return False
    
    def notify_agent_response(self, lead_id: str, lead_data: Dict[str, Any], 
                            response_type: str, response_content: str = "") -> bool:
        """
        Notify Eric about an agent's response
        
        Args:
            lead_id (str): The ID of the lead
            lead_data (Dict[str, Any]): The lead data
            response_type (str): Type of response (positive, negative, neutral)
            response_content (str): Content of the response
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            agent_info = {
                "first_name": lead_data.get("first_name", ""),
                "last_name": lead_data.get("last_name", ""),
                "phone": lead_data.get("phone", ""),
                "property_address": lead_data.get("property_address", "")
            }
            
            # Determine priority based on response type
            priority = NotificationPriority.HIGH if response_type == "positive" else NotificationPriority.MEDIUM
            
            # Create response notification message
            message = f"""Agent Response Update:

Agent: {agent_info['first_name']} {agent_info['last_name']}
Property: {agent_info['property_address']}
Response Type: {response_type.upper()}

Response Content:
{response_content}

Lead ID: {lead_id}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            # Create notification
            notification = EricNotification(
                notification_type=NotificationType.AGENT_RESPONSE,
                priority=priority,
                title=f"📧 Agent Response: {agent_info['first_name']} {agent_info['last_name']}",
                message=message,
                lead_id=lead_id,
                agent_info=agent_info,
                action_required=response_type == "positive"
            )
            
            return self._send_notification(notification)
            
        except Exception as e:
            logger.error(f"Error notifying about agent response for lead {lead_id}: {str(e)}")
            return False
    
    def _identify_hot_indicators(self, lead_data: Dict[str, Any]) -> List[str]:
        """Identify what makes this a hot lead"""
        indicators = []
        
        # Combine text fields for analysis
        text_fields = [
            lead_data.get("notes", ""),
            lead_data.get("situation", ""),
            lead_data.get("motivation", ""),
            lead_data.get("comments", ""),
            lead_data.get("tags", "")
        ]
        combined_text = " ".join(text_fields).lower()
        
        # Check for hot indicators
        hot_keywords = {
            "urgent": "Urgent situation",
            "asap": "ASAP timeline",
            "foreclosure": "Foreclosure risk",
            "behind on payments": "Behind on payments",
            "divorce": "Divorce situation",
            "off-market": "Off-market opportunity",
            "exclusive": "Exclusive listing",
            "motivated seller": "Motivated seller",
            "quick sale": "Quick sale needed",
            "cash needed": "Cash needed urgently"
        }
        
        for keyword, description in hot_keywords.items():
            if keyword in combined_text:
                indicators.append(description)
        
        # Check if property address was provided
        if lead_data.get("property_address"):
            indicators.append("Property address provided")
        
        return indicators
    
    def _format_hot_lead_message(self, agent_info: Dict[str, Any], 
                                hot_indicators: List[str], actions_taken: List[str]) -> str:
        """Format hot lead notification message"""
        message = f"""🚨 HOT LEAD ALERT 🚨

Agent: {agent_info['first_name']} {agent_info['last_name']}
Phone: {agent_info['phone']}
Property: {agent_info['property_address']}

Hot Lead Indicators:
{chr(10).join(f"• {indicator}" for indicator in hot_indicators)}

Actions Taken:
{chr(10).join(f"✅ {action}" for action in actions_taken)}

Next Steps:
- Monitor agent response
- Complete property analysis
- Generate competitive offer
- Follow up within 24 hours

Priority: CRITICAL
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return message
    
    def _format_call_request_message(self, agent_info: Dict[str, Any], 
                                   call_reason: str, lead_data: Dict[str, Any]) -> str:
        """Format call request notification message"""
        message = f"""🔥 CALL REQUEST - {agent_info['first_name']} {agent_info['last_name']}

Lead Details:
- Agent: {agent_info['first_name']} {agent_info['last_name']}
- Phone: {agent_info['phone']}
- Email: {agent_info['email']}
- Property: {agent_info['property_address']}
- Company: {agent_info.get('company', 'N/A')}
- Lead Source: {agent_info.get('lead_source', 'N/A')}

Reason for Call:
{call_reason}

Lead Summary:
{lead_data.get('notes', 'No additional notes')}

Requested by: AI Agent System
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return message
    
    def _send_notification(self, notification: EricNotification) -> bool:
        """Send notification to Eric via GHL"""
        try:
            if not self.eric_contact_id or self.eric_contact_id == "ERIC_CONTACT_ID_PLACEHOLDER":
                logger.warning("Eric contact ID not configured, logging notification instead")
                logger.info(f"NOTIFICATION FOR ERIC: {notification.title}\n{notification.message}")
                return True
            
            # Send as SMS for immediate attention
            response = self.ghl_client.send_sms(self.eric_contact_id, 
                                              f"{notification.title}\n\n{notification.message}")
            
            success = "error" not in response
            
            if success:
                logger.info(f"Successfully sent notification to Eric: {notification.title}")
            else:
                logger.error(f"Failed to send notification to Eric: {response.get('error')}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending notification to Eric: {str(e)}")
            return False


def notify_eric_hot_lead(lead_id: str, lead_data: Dict[str, Any], 
                        actions_taken: List[str] = None) -> bool:
    """
    Helper function to notify Eric about a hot lead
    
    Args:
        lead_id (str): The ID of the hot lead
        lead_data (Dict[str, Any]): The lead data
        actions_taken (List[str]): List of actions already taken
        
    Returns:
        bool: True if notification was sent successfully
    """
    manager = EricNotificationManager()
    return manager.notify_hot_lead(lead_id, lead_data, actions_taken)

def request_eric_call(lead_id: str, lead_data: Dict[str, Any], 
                     call_reason: str, priority: str = "high") -> bool:
    """
    Helper function to request Eric to call an agent
    
    Args:
        lead_id (str): The ID of the lead
        lead_data (Dict[str, Any]): The lead data
        call_reason (str): Reason for the call request
        priority (str): Priority level (critical, high, medium, low)
        
    Returns:
        bool: True if notification was sent successfully
    """
    manager = EricNotificationManager()
    priority_enum = NotificationPriority(priority.lower())
    return manager.request_call(lead_id, lead_data, call_reason, priority_enum)
