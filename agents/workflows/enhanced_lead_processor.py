#!/usr/bin/env python3
"""
Enhanced Lead Processing Automation System

This module provides comprehensive lead processing including:
- Advanced lead scoring with multiple criteria
- Intelligent qualification based on market data
- Automated follow-up sequences with personalization
- Property analysis integration
- Lead nurturing workflows
"""

import os
import logging
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from agents.agent_base import AgentBase
from agents.lead_scoring_bot import LeadScoringBot
from agents.workflows.tier_classifier import classify_lead_tier, is_hot_lead
from agents.workflows.agent_communication_workflow import execute_agent_communication
from agents.workflows.eric_notification_manager import notify_eric_hot_lead, request_eric_call
from agents.workflows.comping_workflow import run_comping_workflow
from agents.mao_calculator import MAOCalculator
from agents.followup_bot import FollowUpBot
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LeadStatus(Enum):
    """Lead status enumeration"""
    NEW = "new"
    QUALIFIED = "qualified"
    ANALYZING = "analyzing"
    OFFER_READY = "offer_ready"
    OFFER_SENT = "offer_sent"
    NEGOTIATING = "negotiating"
    NURTURING = "nurturing"
    CLOSED = "closed"
    DISQUALIFIED = "disqualified"

class LeadPriority(Enum):
    """Lead priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class LeadScore:
    """Lead scoring result"""
    total_score: float
    tier: int
    priority: LeadPriority
    confidence: float
    factors: Dict[str, float]
    recommendations: List[str]
    next_actions: List[str]

@dataclass
class QualificationResult:
    """Lead qualification result"""
    qualified: bool
    qualification_score: float
    disqualification_reasons: List[str]
    qualification_factors: Dict[str, Any]
    recommended_actions: List[str]

class EnhancedLeadProcessor(AgentBase):
    """
    Enhanced lead processor with advanced scoring, qualification, and automation
    """

    def __init__(self, lead_id: str, entity: str = "lead"):
        super().__init__(agent_name="enhanced_lead_processor", lead_id=lead_id, entity=entity)
        self.lead_data = getattr(self, 'context', {}).get("lead", {})
        self.scoring_weights = self._load_scoring_weights()
        self.qualification_criteria = self._load_qualification_criteria()

    def _load_scoring_weights(self) -> Dict[str, float]:
        """Load scoring weights from knowledge base or defaults"""
        try:
            weights_context = query_kb("What are the lead scoring weights and criteria?")
            # Parse weights from context or use defaults optimized for WHOLESALING
            return {
                "equity_amount": 0.25,        # Most important - need equity for deals
                "motivation_level": 0.25,     # Critical - motivated sellers = better deals
                "property_condition": 0.20,   # High importance - distressed = opportunity
                "location_desirability": 0.15, # CRITICAL - bad location kills deals
                "timeline_urgency": 0.10,     # Important - quick sales = less competition
                "property_value": 0.05        # Least critical - we work all price ranges
            }
        except Exception as e:
            logger.warning(f"Could not load scoring weights: {e}, using defaults")
            return {
                "equity_amount": 0.25,        # Most important - need equity for deals
                "motivation_level": 0.25,     # Critical - motivated sellers = better deals
                "property_condition": 0.20,   # High importance - distressed = opportunity
                "location_desirability": 0.15, # CRITICAL - bad location kills deals
                "timeline_urgency": 0.10,     # Important - quick sales = less competition
                "property_value": 0.05        # Least critical - we work all price ranges
            }

    def _load_qualification_criteria(self) -> Dict[str, Any]:
        """Load qualification criteria from knowledge base or defaults"""
        try:
            criteria_context = query_kb("What are the lead qualification criteria and thresholds?")
            # Parse criteria from context or use defaults optimized for WHOLESALING
            return {
                "min_equity": 30000,           # Lower threshold - we work smaller deals
                "max_property_value": 750000,  # Higher threshold - luxury wholesaling exists
                "min_property_value": 50000,   # Lower threshold - mobile homes, condos, etc.
                "min_equity_percentage": 0.15, # At least 15% equity for deal viability
                "required_motivation_indicators": [
                    "foreclosure", "pre-foreclosure", "bankruptcy", "divorce",
                    "inherited", "probate", "behind on payments", "tax liens",
                    "tired landlord", "financial hardship", "need cash fast",
                    "job loss", "medical bills", "urgent", "asap"
                ],
                "disqualifying_factors": [
                    "not motivated", "just curious", "testing market", "want top dollar",
                    "retail price", "full market value", "perfect condition",
                    "recently renovated", "no rush", "will wait"
                ],
                "positive_condition_indicators": [
                    "needs work", "fixer", "handyman special", "as-is",
                    "distressed", "rehab", "gut job", "tear down"
                ]
            }
        except Exception as e:
            logger.warning(f"Could not load qualification criteria: {e}, using defaults")
            return {
                "min_equity": 30000,           # Lower threshold - we work smaller deals
                "max_property_value": 750000,  # Higher threshold - luxury wholesaling exists
                "min_property_value": 50000,   # Lower threshold - mobile homes, condos, etc.
                "min_equity_percentage": 0.15, # At least 15% equity for deal viability
                "required_motivation_indicators": [
                    "foreclosure", "pre-foreclosure", "bankruptcy", "divorce",
                    "inherited", "probate", "behind on payments", "tax liens",
                    "tired landlord", "financial hardship", "need cash fast",
                    "job loss", "medical bills", "urgent", "asap"
                ],
                "disqualifying_factors": [
                    "not motivated", "just curious", "testing market", "want top dollar",
                    "retail price", "full market value", "perfect condition",
                    "recently renovated", "no rush", "will wait"
                ],
                "positive_condition_indicators": [
                    "needs work", "fixer", "handyman special", "as-is",
                    "distressed", "rehab", "gut job", "tear down"
                ]
            }

    def calculate_advanced_score(self) -> LeadScore:
        """
        Calculate advanced lead score using multiple weighted factors

        Returns:
            LeadScore: Comprehensive scoring result
        """
        factors = {}
        total_score = 0.0

        # 1. Property Value Score (0-100)
        property_value = self._extract_numeric_value(self.lead_data.get("property_value", 0))
        if property_value > 0:
            # Optimal range: $150k - $400k
            if 150000 <= property_value <= 400000:
                value_score = 100
            elif property_value < 150000:
                value_score = max(0, (property_value / 150000) * 80)
            else:
                # Diminishing returns for higher values
                value_score = max(20, 100 - ((property_value - 400000) / 10000))
        else:
            value_score = 0

        factors["property_value"] = value_score
        total_score += value_score * self.scoring_weights["property_value"]

        # 2. Equity Amount Score (0-100)
        equity = self._extract_numeric_value(self.lead_data.get("equity", 0))
        if equity >= 100000:
            equity_score = 100
        elif equity >= 50000:
            equity_score = 80
        elif equity >= 25000:
            equity_score = 60
        elif equity > 0:
            equity_score = 40
        else:
            equity_score = 0

        factors["equity_amount"] = equity_score
        total_score += equity_score * self.scoring_weights["equity_amount"]

        # 3. Motivation Level Score (0-100)
        motivation_score = self._calculate_motivation_score()
        factors["motivation_level"] = motivation_score
        total_score += motivation_score * self.scoring_weights["motivation_level"]

        # 4. Timeline Urgency Score (0-100)
        timeline_score = self._calculate_timeline_score()
        factors["timeline_urgency"] = timeline_score
        total_score += timeline_score * self.scoring_weights["timeline_urgency"]

        # 5. Property Condition Score (0-100) - WHOLESALING: Worse condition = Higher score
        condition_score = self._calculate_wholesaling_condition_score()
        factors["property_condition"] = condition_score
        total_score += condition_score * self.scoring_weights["property_condition"]

        # 6. Location Desirability Score (0-100)
        location_score = self._calculate_location_score()
        factors["location_desirability"] = location_score
        total_score += location_score * self.scoring_weights["location_desirability"]

        # 7. Off-Market/Listing Status Bonus (0-20 bonus points)
        listing_bonus = self._calculate_listing_status_bonus()
        factors["listing_status_bonus"] = listing_bonus
        total_score += listing_bonus  # Direct bonus, not weighted

        # Determine tier and priority
        tier = 1 if total_score >= 70 else 2

        if total_score >= 85:
            priority = LeadPriority.CRITICAL
        elif total_score >= 70:
            priority = LeadPriority.HIGH
        elif total_score >= 50:
            priority = LeadPriority.MEDIUM
        else:
            priority = LeadPriority.LOW

        # Calculate confidence based on data completeness
        confidence = self._calculate_confidence()

        # Generate recommendations
        recommendations = self._generate_recommendations(total_score, factors)

        # Generate next actions
        next_actions = self._generate_next_actions(tier, priority, total_score)

        return LeadScore(
            total_score=total_score,
            tier=tier,
            priority=priority,
            confidence=confidence,
            factors=factors,
            recommendations=recommendations,
            next_actions=next_actions
        )

    def _calculate_motivation_score(self) -> float:
        """
        Calculate motivation score based on text analysis - WHOLESALING FOCUSED

        Wholesalers need highly motivated sellers who will accept below-market offers
        """
        # Combine text fields
        text_fields = [
            self.lead_data.get("motivation", ""),
            self.lead_data.get("situation", ""),
            self.lead_data.get("notes", ""),
            self.lead_data.get("comments", "")
        ]
        combined_text = " ".join(text_fields).lower()

        # CRITICAL motivation indicators for wholesaling (highest scores)
        critical_motivation = [
            "foreclosure", "pre-foreclosure", "sheriff sale", "auction",
            "bankruptcy", "chapter 7", "chapter 13", "behind on payments",
            "tax liens", "tax sale", "irs liens", "probate", "estate sale",
            "divorce", "death in family", "inherited", "can't afford payments",
            "job loss", "unemployment", "medical bills", "financial hardship",
            "need cash now", "need to sell fast", "urgent", "asap", "immediately",
            "agent referral", "agent relationship", "pocket listing", "off market"
        ]

        # HIGH motivation indicators for wholesaling
        high_motivation = [
            "tired landlord", "problem tenant", "eviction", "vacancy",
            "relocation", "job transfer", "military deployment",
            "downsizing", "retirement", "health issues", "disability",
            "property management headache", "negative cash flow",
            "can't rent", "bad neighborhood", "declining area"
        ]

        # MEDIUM motivation indicators
        medium_motivation = [
            "upgrading", "moving", "new home", "growing family",
            "maintenance issues", "old house", "needs work",
            "don't want to fix", "as-is sale", "no repairs"
        ]

        # LOW motivation indicators (bad for wholesaling)
        low_motivation = [
            "just curious", "testing market", "might sell", "maybe",
            "not sure", "thinking about it", "exploring options",
            "want top dollar", "retail price", "full market value"
        ]

        # DISQUALIFYING indicators (very bad for wholesaling)
        disqualifying = [
            "not motivated", "no rush", "perfect condition", "recently renovated",
            "want maximum price", "will wait for right offer", "not desperate"
        ]

        score = 40  # Lower base score - need clear motivation indicators

        # Score the motivation level
        for indicator in critical_motivation:
            if indicator in combined_text:
                score += 20  # Big boost for critical indicators

        for indicator in high_motivation:
            if indicator in combined_text:
                score += 12  # Good boost for high motivation

        for indicator in medium_motivation:
            if indicator in combined_text:
                score += 6   # Modest boost for medium motivation

        for indicator in low_motivation:
            if indicator in combined_text:
                score -= 15  # Penalty for low motivation

        for indicator in disqualifying:
            if indicator in combined_text:
                score -= 30  # Heavy penalty for disqualifying factors

        return max(0, min(100, score))

    def _calculate_timeline_score(self) -> float:
        """Calculate timeline urgency score"""
        timeline_text = self.lead_data.get("timeline", "").lower()

        if any(word in timeline_text for word in ["asap", "immediately", "urgent", "this week"]):
            return 100
        elif any(word in timeline_text for word in ["soon", "next month", "30 days"]):
            return 80
        elif any(word in timeline_text for word in ["few months", "3 months", "90 days"]):
            return 60
        elif any(word in timeline_text for word in ["6 months", "next year"]):
            return 40
        else:
            return 50  # Default if no timeline specified

    def _calculate_wholesaling_condition_score(self) -> float:
        """
        Calculate property condition score for WHOLESALING

        For wholesalers, worse condition = higher score because:
        - Distressed properties sell at deeper discounts
        - More profit margin for both wholesaler and end investor
        - Less competition from retail buyers
        - Motivated sellers more likely to accept quick cash offers
        """
        condition_text = self.lead_data.get("condition", "").lower()

        # High score for distressed properties (wholesaler goldmine)
        if any(word in condition_text for word in ["poor", "major repairs", "fixer", "tear down", "condemned", "fire damage", "flood damage"]):
            return 100  # Perfect for wholesaling!
        elif any(word in condition_text for word in ["needs work", "rehab", "gut job", "structural issues", "foundation problems"]):
            return 90   # Excellent wholesale opportunity
        elif any(word in condition_text for word in ["fair", "cosmetic repairs", "dated", "needs updating", "handyman special"]):
            return 75   # Good wholesale potential
        elif any(word in condition_text for word in ["good", "minor repairs", "livable", "rent ready"]):
            return 50   # Some wholesale potential
        elif any(word in condition_text for word in ["excellent", "move-in ready", "updated", "renovated", "turnkey"]):
            return 20   # Low wholesale potential (retail competition)
        else:
            return 60   # Default if no condition specified

    def _calculate_condition_score(self) -> float:
        """Legacy method - redirects to wholesaling-specific scoring"""
        return self._calculate_wholesaling_condition_score()

    def _calculate_location_score(self) -> float:
        """
        Calculate location desirability score for WHOLESALING

        For wholesalers, location is critical because:
        - Bad locations = no investor interest = no exit strategy
        - Good locations with distressed properties = golden opportunity
        - Crime rates and resale values matter for investor buyers
        """
        zip_code = self.lead_data.get("zip_code", "")
        city = self.lead_data.get("city", "").lower()
        state = self.lead_data.get("state", "").lower()

        # Michigan market specific scoring
        if state in ["michigan", "mi"]:
            return self._calculate_michigan_location_score(city, zip_code)

        # General location scoring for other markets
        return self._calculate_general_location_score(city, state)

    def _calculate_michigan_location_score(self, city: str, zip_code: str) -> float:
        """Michigan-specific location scoring for wholesaling"""

        # EXCELLENT wholesale locations in Michigan (90-100 points)
        excellent_areas = [
            "grand rapids", "ann arbor", "troy", "novi", "farmington hills",
            "rochester hills", "sterling heights", "livonia", "dearborn",
            "west bloomfield", "birmingham", "royal oak", "ferndale"
        ]

        # GOOD wholesale locations (70-85 points)
        good_areas = [
            "lansing", "kalamazoo", "battle creek", "jackson", "holland",
            "midland", "traverse city", "portage", "wyoming", "kentwood",
            "warren", "southfield", "taylor", "lincoln park"
        ]

        # FAIR wholesale locations (50-65 points)
        fair_areas = [
            "detroit suburbs", "redford", "garden city", "westland",
            "inkster", "romulus", "canton", "plymouth", "ypsilanti"
        ]

        # POOR wholesale locations (20-35 points) - High crime, low resale
        poor_areas = [
            "detroit", "flint", "pontiac", "saginaw", "bay city",
            "muskegon", "benton harbor", "highland park", "hamtramck"
        ]

        # AVOID locations (0-15 points) - No investor interest
        avoid_areas = [
            "detroit downtown", "detroit east side", "detroit southwest",
            "flint north side", "pontiac downtown"
        ]

        # Check specific areas
        for area in excellent_areas:
            if area in city:
                return 95

        for area in good_areas:
            if area in city:
                return 78

        for area in fair_areas:
            if area in city:
                return 58

        for area in poor_areas:
            if area in city:
                return 28

        for area in avoid_areas:
            if area in city:
                return 10

        # ZIP code specific scoring for Detroit metro
        if zip_code:
            return self._score_michigan_zip_code(zip_code)

        return 50  # Default for unknown Michigan areas

    def _score_michigan_zip_code(self, zip_code: str) -> float:
        """Score specific Michigan ZIP codes"""

        # Premium ZIP codes (investor favorites)
        premium_zips = [
            "48067", "48084", "48098", "48104", "48105", "48108",  # Troy, Novi, Ann Arbor area
            "48322", "48323", "48324", "48331", "48334",          # Farmington Hills, West Bloomfield
            "48073", "48083", "48307", "48309"                    # Royal Oak, Birmingham area
        ]

        # Good ZIP codes
        good_zips = [
            "49503", "49504", "49506", "49508",  # Grand Rapids
            "48911", "48912", "48917",           # Lansing
            "49001", "49008", "49009"            # Kalamazoo
        ]

        # Avoid ZIP codes (Detroit problem areas)
        avoid_zips = [
            "48201", "48202", "48204", "48205", "48206", "48207",  # Detroit east side
            "48208", "48209", "48210", "48212", "48213", "48214",  # Detroit central/southwest
            "48215", "48216", "48217", "48219", "48221", "48224"   # Detroit problem areas
        ]

        if zip_code in premium_zips:
            return 95
        elif zip_code in good_zips:
            return 78
        elif zip_code in avoid_zips:
            return 15

        return 50  # Default

    def _calculate_general_location_score(self, city: str, state: str) -> float:
        """General location scoring for non-Michigan markets"""

        # High-demand wholesale markets
        if city in ["atlanta", "nashville", "charlotte", "raleigh", "austin", "phoenix"]:
            return 90
        elif city in ["birmingham", "knoxville", "chattanooga", "columbus", "indianapolis"]:
            return 75
        elif city in ["memphis", "little rock", "jackson", "mobile"]:
            return 60
        else:
            return 55  # Default for unknown areas

    def _calculate_listing_status_bonus(self) -> float:
        """
        Calculate bonus points for off-market deals and listing status

        Off-market deals are GOLD for wholesalers because:
        - No retail competition
        - Motivated sellers (why else go off-market?)
        - Better negotiation position
        - Faster closing potential
        """

        # Check various fields for listing status indicators
        text_fields = [
            self.lead_data.get("source", ""),
            self.lead_data.get("lead_source", ""),
            self.lead_data.get("notes", ""),
            self.lead_data.get("comments", ""),
            self.lead_data.get("listing_status", ""),
            self.lead_data.get("marketing_status", "")
        ]
        combined_text = " ".join(text_fields).lower()

        # HIGHEST BONUS: Off-market/pocket listings (20 points)
        off_market_indicators = [
            "off market", "off-market", "pocket listing", "not listed",
            "private sale", "agent referral", "agent relationship",
            "exclusive", "pre-market", "coming soon", "whisper listing"
        ]

        # HIGH BONUS: Stale listings (15 points)
        stale_listing_indicators = [
            "90 days", "120 days", "6 months", "expired listing",
            "price reduction", "multiple reductions", "been listed",
            "long time on market", "can't sell", "no offers"
        ]

        # MEDIUM BONUS: Distressed listings (10 points)
        distressed_listing_indicators = [
            "damaged property", "fire damage", "flood damage",
            "vandalized", "needs major work", "as-is listing",
            "investor special", "cash only", "no financing"
        ]

        # SMALL BONUS: Seller finance potential (5 points)
        seller_finance_indicators = [
            "seller financing", "owner financing", "terms available",
            "flexible terms", "creative financing", "lease option",
            "rent to own", "contract for deed"
        ]

        # Check for indicators and assign bonus
        for indicator in off_market_indicators:
            if indicator in combined_text:
                return 20  # JACKPOT - off-market deal

        for indicator in stale_listing_indicators:
            if indicator in combined_text:
                return 15  # Great opportunity - motivated seller

        for indicator in distressed_listing_indicators:
            if indicator in combined_text:
                return 10  # Good opportunity - less competition

        for indicator in seller_finance_indicators:
            if indicator in combined_text:
                return 5   # Some opportunity - creative terms

        # Check if it's a fresh MLS listing (penalty)
        fresh_listing_indicators = [
            "just listed", "new listing", "fresh on market",
            "day 1", "first day", "brand new listing"
        ]

        for indicator in fresh_listing_indicators:
            if indicator in combined_text:
                return -5  # Penalty - retail competition

        return 0  # No bonus or penalty

    def _calculate_confidence(self) -> float:
        """Calculate confidence score based on data completeness"""
        required_fields = [
            "property_value", "equity", "motivation", "condition",
            "timeline", "first_name", "phone", "email"
        ]

        completed_fields = sum(1 for field in required_fields
                             if self.lead_data.get(field))

        return (completed_fields / len(required_fields)) * 100

    def _generate_recommendations(self, total_score: float, factors: Dict[str, float], lead_source: str = "direct") -> List[str]:
        """Generate recommendations based on scoring factors and lead source"""
        recommendations = []
        
        # Realtor-specific recommendations
        if lead_source == "realtor":
            if factors.get("response_rate", 0) < 60:
                recommendations.append("Increase daily text volume to 400-500")
            
            if factors.get("tier1_conversion", 0) < 50:
                recommendations.append("Improve follow-up with tier 1 agents - they're your money bucket")
            
            if factors.get("tier3_followup", 0) < 70:
                recommendations.append("Set up automated follow-up sequence for tier 3 agents")
        else:
            # Original direct-to-seller recommendations
            if factors.get("property_value", 0) < 50:
                recommendations.append("Verify property value with recent comps")
            
            if factors.get("equity_amount", 0) < 60:
                recommendations.append("Confirm equity amount and outstanding liens")
        
        return recommendations

    def _generate_next_actions(self, tier: int, priority: LeadPriority, score: float) -> List[str]:
        """Generate next actions based on tier and priority"""
        actions = []

        if tier == 1:
            actions.extend([
                "Run property comping analysis",
                "Calculate MAO (Maximum Allowable Offer)",
                "Generate initial offer"
            ])

            if priority in [LeadPriority.CRITICAL, LeadPriority.HIGH]:
                actions.append("Schedule follow-up within 2 hours")
            else:
                actions.append("Schedule follow-up within 24 hours")
        else:
            actions.extend([
                "Add to nurture campaign",
                "Schedule follow-up in 3-5 days",
                "Monitor for status changes"
            ])

        return actions

    def qualify_lead(self) -> QualificationResult:
        """
        Perform detailed lead qualification

        Returns:
            QualificationResult: Qualification analysis result
        """
        qualified = True
        qualification_score = 100.0
        disqualification_reasons = []
        qualification_factors = {}

        # Check minimum equity requirement
        equity = self._extract_numeric_value(self.lead_data.get("equity", 0))
        if equity < self.qualification_criteria["min_equity"]:
            qualified = False
            qualification_score -= 30
            disqualification_reasons.append(f"Insufficient equity: ${equity:,.0f} < ${self.qualification_criteria['min_equity']:,.0f}")

        qualification_factors["equity_check"] = equity >= self.qualification_criteria["min_equity"]

        # Check property value range
        property_value = self._extract_numeric_value(self.lead_data.get("property_value", 0))
        if property_value > 0:
            if property_value < self.qualification_criteria["min_property_value"]:
                qualified = False
                qualification_score -= 25
                disqualification_reasons.append(f"Property value too low: ${property_value:,.0f}")
            elif property_value > self.qualification_criteria["max_property_value"]:
                qualification_score -= 15  # Not disqualifying but reduces score
                disqualification_reasons.append(f"Property value high: ${property_value:,.0f} (requires special handling)")

        qualification_factors["value_range_check"] = (
            self.qualification_criteria["min_property_value"] <= property_value <= self.qualification_criteria["max_property_value"]
        )

        # Check for motivation indicators
        text_fields = [
            self.lead_data.get("motivation", ""),
            self.lead_data.get("situation", ""),
            self.lead_data.get("notes", ""),
            self.lead_data.get("comments", "")
        ]
        combined_text = " ".join(text_fields).lower()

        found_motivation = False
        for indicator in self.qualification_criteria["required_motivation_indicators"]:
            if indicator in combined_text:
                found_motivation = True
                break

        if not found_motivation:
            qualification_score -= 20
            disqualification_reasons.append("No clear motivation indicators found")

        qualification_factors["motivation_check"] = found_motivation

        # Check for disqualifying factors
        disqualifying_found = []
        for factor in self.qualification_criteria["disqualifying_factors"]:
            if factor in combined_text:
                disqualifying_found.append(factor)

        if disqualifying_found:
            qualified = False
            qualification_score -= 40
            disqualification_reasons.append(f"Disqualifying factors: {', '.join(disqualifying_found)}")

        qualification_factors["disqualifying_factors"] = disqualifying_found

        # Generate recommended actions
        recommended_actions = []
        if qualified:
            recommended_actions.extend([
                "Proceed with property analysis",
                "Schedule seller interview",
                "Begin comping process"
            ])
        else:
            recommended_actions.extend([
                "Add to nurture sequence",
                "Request additional information",
                "Monitor for status changes"
            ])

        return QualificationResult(
            qualified=qualified,
            qualification_score=max(0, qualification_score),
            disqualification_reasons=disqualification_reasons,
            qualification_factors=qualification_factors,
            recommended_actions=recommended_actions
        )

    def create_follow_up_sequence(self, lead_score: LeadScore) -> List[Dict[str, Any]]:
        """
        Create personalized follow-up sequence based on lead score

        Args:
            lead_score: Lead scoring result

        Returns:
            List of follow-up actions with timing and content
        """
        sequence = []

        if lead_score.priority == LeadPriority.CRITICAL:
            # Immediate and aggressive follow-up
            sequence = [
                {
                    "delay_hours": 0.5,
                    "type": "phone_call",
                    "message": "Immediate phone call - high priority lead",
                    "priority": "critical"
                },
                {
                    "delay_hours": 2,
                    "type": "sms",
                    "message": "Follow-up text if no answer",
                    "priority": "high"
                },
                {
                    "delay_hours": 24,
                    "type": "email",
                    "message": "Detailed email with offer preview",
                    "priority": "high"
                }
            ]
        elif lead_score.priority == LeadPriority.HIGH:
            # Standard high-priority follow-up
            sequence = [
                {
                    "delay_hours": 2,
                    "type": "phone_call",
                    "message": "Initial contact call",
                    "priority": "high"
                },
                {
                    "delay_hours": 24,
                    "type": "sms",
                    "message": "Follow-up text message",
                    "priority": "medium"
                },
                {
                    "delay_hours": 72,
                    "type": "email",
                    "message": "Detailed follow-up email",
                    "priority": "medium"
                }
            ]
        else:
            # Standard nurture sequence
            sequence = [
                {
                    "delay_hours": 24,
                    "type": "email",
                    "message": "Welcome email with information",
                    "priority": "medium"
                },
                {
                    "delay_hours": 120,  # 5 days
                    "type": "sms",
                    "message": "Check-in text message",
                    "priority": "low"
                },
                {
                    "delay_hours": 240,  # 10 days
                    "type": "email",
                    "message": "Market update email",
                    "priority": "low"
                }
            ]

        return sequence

    def run(self) -> Dict[str, Any]:
        """
        Run the enhanced lead processing workflow

        Returns:
            Dict containing processing results
        """
        try:
            logger.info(f"Starting enhanced lead processing for lead {self.lead_id}")

            # Step 1: Calculate advanced lead score
            lead_score = self.calculate_advanced_score()
            logger.info(f"Lead score calculated: {lead_score.total_score:.1f} (Tier {lead_score.tier}, {lead_score.priority.value})")

            # Step 1.5: Check if this is a hot lead requiring immediate action
            is_hot = is_hot_lead(self.lead_data)
            if is_hot and lead_score.tier == 1:
                logger.info(f"HOT LEAD DETECTED for lead {self.lead_id} - executing immediate communication")

                # Execute immediate agent communication
                comm_result = execute_agent_communication(self.lead_id, self.lead_data)
                logger.info(f"Agent communication result: {comm_result}")

                # Notify Eric about the hot lead
                actions_taken = []
                if comm_result.get("success"):
                    for result in comm_result.get("results", []):
                        if result.get("success"):
                            actions_taken.append(f"{result['message_type'].upper()} sent using {result['template_used']}")

                notify_result = notify_eric_hot_lead(self.lead_id, self.lead_data, actions_taken)
                logger.info(f"Eric notification result: {notify_result}")

            # Step 2: Perform qualification
            qualification = self.qualify_lead()
            logger.info(f"Lead qualification: {'QUALIFIED' if qualification.qualified else 'NOT QUALIFIED'} (Score: {qualification.qualification_score:.1f})")

            # Step 3: Create follow-up sequence
            follow_up_sequence = self.create_follow_up_sequence(lead_score)
            logger.info(f"Created follow-up sequence with {len(follow_up_sequence)} actions")

            # Step 4: Update lead status and metadata
            self._update_lead_metadata(lead_score, qualification)

            # Step 5: Trigger next actions based on results
            next_actions_triggered = self._trigger_next_actions(lead_score, qualification)

            return {
                "success": True,
                "lead_id": self.lead_id,
                "lead_score": {
                    "total_score": lead_score.total_score,
                    "tier": lead_score.tier,
                    "priority": lead_score.priority.value,
                    "confidence": lead_score.confidence,
                    "factors": lead_score.factors,
                    "recommendations": lead_score.recommendations,
                    "next_actions": lead_score.next_actions
                },
                "qualification": {
                    "qualified": qualification.qualified,
                    "score": qualification.qualification_score,
                    "reasons": qualification.disqualification_reasons,
                    "factors": qualification.qualification_factors,
                    "recommended_actions": qualification.recommended_actions
                },
                "follow_up_sequence": follow_up_sequence,
                "actions_triggered": next_actions_triggered,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in enhanced lead processing: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "lead_id": self.lead_id,
                "timestamp": datetime.utcnow().isoformat()
            }

    def _update_lead_metadata(self, lead_score: LeadScore, qualification: QualificationResult):
        """Update lead metadata in the database"""
        try:
            # This would update the lead record in Supabase
            # For now, we'll log the updates that would be made
            logger.info(f"Would update lead {self.lead_id} with:")
            logger.info(f"  - Score: {lead_score.total_score}")
            logger.info(f"  - Tier: {lead_score.tier}")
            logger.info(f"  - Priority: {lead_score.priority.value}")
            logger.info(f"  - Qualified: {qualification.qualified}")
            logger.info(f"  - Status: {LeadStatus.QUALIFIED.value if qualification.qualified else LeadStatus.NURTURING.value}")
        except Exception as e:
            logger.error(f"Error updating lead metadata: {str(e)}")

    def _trigger_next_actions(self, lead_score: LeadScore, qualification: QualificationResult) -> List[str]:
        """Trigger next actions based on processing results"""
        triggered_actions = []

        try:
            if qualification.qualified and lead_score.tier == 1:
                # Trigger comping workflow
                logger.info(f"Triggering comping workflow for lead {self.lead_id}")
                triggered_actions.append("comping_workflow")

                # Trigger MAO calculation
                logger.info(f"Triggering MAO calculation for lead {self.lead_id}")
                triggered_actions.append("mao_calculation")

                if lead_score.priority in [LeadPriority.CRITICAL, LeadPriority.HIGH]:
                    # Trigger immediate follow-up
                    logger.info(f"Triggering immediate follow-up for lead {self.lead_id}")
                    triggered_actions.append("immediate_followup")
            else:
                # Add to nurture sequence
                logger.info(f"Adding lead {self.lead_id} to nurture sequence")
                triggered_actions.append("nurture_sequence")

        except Exception as e:
            logger.error(f"Error triggering next actions: {str(e)}")

        return triggered_actions

    @staticmethod
    def _extract_numeric_value(value) -> float:
        """Extract numeric value from string or number"""
        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            try:
                # Remove currency symbols and commas
                cleaned_value = value.replace('$', '').replace(',', '').strip()
                return float(cleaned_value)
            except ValueError:
                return 0.0

        return 0.0


# Convenience functions
def process_lead_enhanced(lead_id: str) -> Dict[str, Any]:
    """
    Process a lead with enhanced automation

    Args:
        lead_id: The ID of the lead to process

    Returns:
        Dict containing processing results
    """
    processor = EnhancedLeadProcessor(lead_id=lead_id)
    return processor.run()


def batch_process_leads(lead_ids: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Process multiple leads with enhanced automation

    Args:
        lead_ids: List of lead IDs to process

    Returns:
        Dict mapping lead IDs to their processing results
    """
    results = {}

    for lead_id in lead_ids:
        try:
            results[lead_id] = process_lead_enhanced(lead_id)
        except Exception as e:
            logger.error(f"Error processing lead {lead_id}: {str(e)}")
            results[lead_id] = {
                "success": False,
                "error": str(e),
                "lead_id": lead_id,
                "timestamp": datetime.utcnow().isoformat()
            }

    return results
