"""
Agent Communication Workflow

Handles immediate communication with real estate agents for hot leads
and manages ongoing relationship building with Tier 2 agents.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from agents.ghl_client import GHLClient
from agents.workflows.tier_classifier import classify_lead_tier, is_hot_lead
from agents.workflows.notification_manager import NotificationManager
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CommunicationType(Enum):
    """Types of communication"""
    EMAIL = "email"
    SMS = "sms"
    BOTH = "both"

class Priority(Enum):
    """Communication priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class CommunicationResult:
    """Result of a communication attempt"""
    success: bool
    message_type: str
    template_used: str
    sent_at: datetime
    error_message: Optional[str] = None
    ghl_response: Optional[Dict] = None

class AgentCommunicationWorkflow:
    """
    Manages communication with real estate agents based on lead tier and priority
    """
    
    def __init__(self, lead_id: str, lead_data: Dict[str, Any]):
        """
        Initialize the agent communication workflow
        
        Args:
            lead_id (str): The ID of the lead
            lead_data (Dict[str, Any]): The lead data from GHL
        """
        self.lead_id = lead_id
        self.lead_data = lead_data
        self.ghl_client = GHLClient()
        self.notification_manager = NotificationManager()
        
        # Extract agent information
        self.agent_info = self._extract_agent_info()
        
        # Determine lead characteristics
        self.tier = classify_lead_tier(lead_data)
        self.is_hot = is_hot_lead(lead_data)
        
        logger.info(f"Initialized agent communication workflow for lead {lead_id}, tier {self.tier}, hot: {self.is_hot}")
    
    def _extract_agent_info(self) -> Dict[str, Any]:
        """Extract agent information from lead data"""
        return {
            "first_name": self.lead_data.get("first_name", ""),
            "last_name": self.lead_data.get("last_name", ""),
            "email": self.lead_data.get("email", ""),
            "phone": self.lead_data.get("phone", ""),
            "ghl_contact_id": self.lead_data.get("ghl_contact_id", ""),
            "property_address": self.lead_data.get("property_address", ""),
            "company": self.lead_data.get("company", ""),
            "title": self.lead_data.get("title", "")
        }
    
    def execute_immediate_communication(self) -> List[CommunicationResult]:
        """
        Execute immediate communication for hot leads
        
        Returns:
            List[CommunicationResult]: Results of communication attempts
        """
        results = []
        
        if not self.is_hot:
            logger.info(f"Lead {self.lead_id} is not a hot lead, skipping immediate communication")
            return results
        
        if self.tier != 1:
            logger.info(f"Lead {self.lead_id} is not Tier 1, skipping immediate communication")
            return results
        
        logger.info(f"Executing immediate communication for hot lead {self.lead_id}")
        
        # Send immediate email
        email_result = self._send_hot_lead_email()
        if email_result:
            results.append(email_result)
        
        # Send immediate SMS
        sms_result = self._send_hot_lead_sms()
        if sms_result:
            results.append(sms_result)
        
        # Log the communication
        self._log_communication_attempt(results)
        
        return results
    
    def _send_hot_lead_email(self) -> Optional[CommunicationResult]:
        """Send hot lead email to agent"""
        try:
            # Get email template
            template_content = self._get_template("hot_lead_immediate_interest_email")
            if not template_content:
                logger.error("Could not retrieve hot lead email template")
                return None
            
            # Format template with agent and property information
            formatted_content = self._format_template(template_content)
            
            # Extract subject from template
            subject = self._extract_subject_from_template(template_content)
            
            # Send email via GHL
            contact_id = self.agent_info.get("ghl_contact_id")
            if not contact_id:
                logger.error(f"No GHL contact ID for lead {self.lead_id}")
                return None
            
            response = self.ghl_client.send_email(contact_id, subject, formatted_content)
            
            return CommunicationResult(
                success="error" not in response,
                message_type="email",
                template_used="hot_lead_immediate_interest_email",
                sent_at=datetime.now(),
                error_message=response.get("error"),
                ghl_response=response
            )
            
        except Exception as e:
            logger.error(f"Error sending hot lead email: {str(e)}")
            return CommunicationResult(
                success=False,
                message_type="email",
                template_used="hot_lead_immediate_interest_email",
                sent_at=datetime.now(),
                error_message=str(e)
            )
    
    def _send_hot_lead_sms(self) -> Optional[CommunicationResult]:
        """Send hot lead SMS to agent"""
        try:
            # Get SMS template
            template_content = self._get_template("hot_lead_immediate_sms")
            if not template_content:
                logger.error("Could not retrieve hot lead SMS template")
                return None
            
            # Format template with agent and property information
            formatted_content = self._format_template(template_content)
            
            # Send SMS via GHL
            contact_id = self.agent_info.get("ghl_contact_id")
            if not contact_id:
                logger.error(f"No GHL contact ID for lead {self.lead_id}")
                return None
            
            response = self.ghl_client.send_sms(contact_id, formatted_content)
            
            return CommunicationResult(
                success="error" not in response,
                message_type="sms",
                template_used="hot_lead_immediate_sms",
                sent_at=datetime.now(),
                error_message=response.get("error"),
                ghl_response=response
            )
            
        except Exception as e:
            logger.error(f"Error sending hot lead SMS: {str(e)}")
            return CommunicationResult(
                success=False,
                message_type="sms",
                template_used="hot_lead_immediate_sms",
                sent_at=datetime.now(),
                error_message=str(e)
            )
    
    def _get_template(self, template_name: str) -> Optional[str]:
        """Get template content from knowledge base"""
        try:
            # Query knowledge base for template
            query = f"Get template content for {template_name}"
            template_content = query_kb(query)
            
            if template_content and len(template_content.strip()) > 0:
                return template_content
            
            # Fallback to default templates if KB query fails
            return self._get_default_template(template_name)
            
        except Exception as e:
            logger.error(f"Error retrieving template {template_name}: {str(e)}")
            return self._get_default_template(template_name)
    
    def _get_default_template(self, template_name: str) -> Optional[str]:
        """Get default template content as fallback"""
        default_templates = {
            "hot_lead_immediate_interest_email": """Subject: RE: {property_address} - Running Numbers Now

Hi {agent_first_name},

Thank you for sharing the property at {property_address} with us. We're very interested and are actively running the numbers on this deal right now to get you a competitive offer.

Our team specializes in quick closings and can typically provide an offer within 24-48 hours.

Best regards,
Eric's Investment Team""",
            
            "hot_lead_immediate_sms": "Hi {agent_first_name}! Thanks for the {property_address} lead. We're running numbers now and will have an offer within 24-48hrs. Quick closing possible.",
            
            "tier2_biweekly_checkin_email": """Subject: Checking In - Any New Deals?

Hi {agent_first_name},

Hope you're doing well! I wanted to check in and see if you have any new deals or off-market opportunities that might be a good fit for our investment criteria.

Thanks for keeping us in mind!

Best regards,
Eric's Investment Team""",
            
            "tier2_biweekly_checkin_sms": "Hi {agent_first_name}! Quick check-in - any new deals or off-market opportunities? We're actively looking and can close fast. Thanks!"
        }
        
        return default_templates.get(template_name)
    
    def _format_template(self, template_content: str) -> str:
        """Format template with lead and agent data"""
        try:
            # Create context for template formatting
            context = {
                "agent_first_name": self.agent_info.get("first_name", ""),
                "agent_last_name": self.agent_info.get("last_name", ""),
                "agent_email": self.agent_info.get("email", ""),
                "agent_phone": self.agent_info.get("phone", ""),
                "property_address": self.agent_info.get("property_address", "the property"),
                "lead_id": self.lead_id,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "company": self.agent_info.get("company", ""),
                "title": self.agent_info.get("title", "")
            }
            
            # Format template
            formatted = template_content.format(**context)
            return formatted
            
        except Exception as e:
            logger.error(f"Error formatting template: {str(e)}")
            return template_content
    
    def _extract_subject_from_template(self, template_content: str) -> str:
        """Extract email subject from template content"""
        lines = template_content.split('\n')
        for line in lines:
            if line.strip().startswith('Subject:'):
                return line.replace('Subject:', '').strip()
        
        # Default subject if not found
        return f"RE: {self.agent_info.get('property_address', 'Property Opportunity')}"
    
    def _log_communication_attempt(self, results: List[CommunicationResult]):
        """Log communication attempts for tracking"""
        try:
            log_data = {
                "lead_id": self.lead_id,
                "agent_info": self.agent_info,
                "tier": self.tier,
                "is_hot": self.is_hot,
                "communication_results": [
                    {
                        "success": result.success,
                        "message_type": result.message_type,
                        "template_used": result.template_used,
                        "sent_at": result.sent_at.isoformat(),
                        "error_message": result.error_message
                    }
                    for result in results
                ],
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Communication log for lead {self.lead_id}: {json.dumps(log_data, indent=2)}")
            
        except Exception as e:
            logger.error(f"Error logging communication attempt: {str(e)}")


def execute_agent_communication(lead_id: str, lead_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute agent communication workflow for a lead
    
    Args:
        lead_id (str): The ID of the lead
        lead_data (Dict[str, Any]): The lead data from GHL
        
    Returns:
        Dict[str, Any]: Results of the communication workflow
    """
    try:
        workflow = AgentCommunicationWorkflow(lead_id, lead_data)
        
        # Execute immediate communication if it's a hot lead
        communication_results = workflow.execute_immediate_communication()
        
        return {
            "success": True,
            "lead_id": lead_id,
            "tier": workflow.tier,
            "is_hot": workflow.is_hot,
            "communications_sent": len(communication_results),
            "results": [
                {
                    "success": result.success,
                    "message_type": result.message_type,
                    "template_used": result.template_used,
                    "error_message": result.error_message
                }
                for result in communication_results
            ],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error executing agent communication workflow: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "lead_id": lead_id,
            "timestamp": datetime.now().isoformat()
        }
