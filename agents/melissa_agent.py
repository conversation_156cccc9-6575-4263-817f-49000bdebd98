#!/usr/bin/env python3
"""
Melissa Data Agent - Property Data and Historical Deeds API Integration
Provides comprehensive property information with 1,000 free credits per month
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from agents.agent_base import Agent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MelissaConfig:
    """Configuration for Melissa Data API"""
    license_key: str
    base_url: str = "https://property.melissadata.net/v4/WEB"
    timeout: int = 30
    max_retries: int = 3
    rate_limit_per_minute: int = 60  # Conservative estimate

class MelissaAgent(Agent):
    """
    Melissa Data Agent for comprehensive property data and historical deeds
    
    Features:
    - 1,000 free credits per month
    - Comprehensive property information
    - Historical deed records
    - Owner information and property ownership history
    - Property characteristics and improvements
    - Assessed values and tax information
    """
    
    def __init__(self, lead_id: str = "melissa_agent", entity: str = "property_data"):
        super().__init__("melissa_agent", lead_id, entity)
        
        # Initialize Melissa configuration
        license_key = os.getenv('MELISSA_LICENSE_KEY')
        if not license_key:
            raise ValueError("MELISSA_LICENSE_KEY environment variable is required")
        
        self.config = MelissaConfig(license_key=license_key)
        self.session = None
        
        logger.info("Melissa Data Agent initialized successfully")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def run(self):
        """Required by Agent base class - not used for async operations"""
        pass
    
    async def lookup_property(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive property information using Melissa's LookupProperty endpoint
        
        Args:
            property_data: Property information including address
            
        Returns:
            Dict containing property details, owner info, and characteristics
        """
        try:
            # Format address for API
            address_line1 = property_data.get('address', '')
            city = property_data.get('city', '')
            state = property_data.get('state', '')
            postal_code = property_data.get('zip_code', '')
            
            params = {
                'id': self.config.license_key,
                't': f"PropertyLookup_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'a1': address_line1,
                'city': city,
                'state': state,
                'postal': postal_code,
                'format': 'JSON'
            }
            
            logger.info(f"Looking up property: {address_line1}, {city}, {state}")
            
            result = await self._make_api_request('/LookupProperty', params)
            
            if result['success']:
                data = result['data']
                records = data.get('Records', [])
                
                if records:
                    property_record = records[0]
                    normalized_property = self._normalize_property_data(property_record)
                    
                    return {
                        'success': True,
                        'property': normalized_property,
                        'source': 'melissa_property_api',
                        'timestamp': datetime.now().isoformat(),
                        'credits_used': 1
                    }
                else:
                    return {
                        'success': False,
                        'error': 'No property records found',
                        'source': 'melissa_property_api'
                    }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error looking up property: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'melissa_property_api'
            }
    
    async def lookup_deeds(self, property_data: Dict[str, Any], page: int = 1) -> Dict[str, Any]:
        """
        Get historical deed records for a property
        
        Args:
            property_data: Property information including address
            page: Page number for pagination (default 1)
            
        Returns:
            Dict containing historical deed records
        """
        try:
            # Format address for API
            address = self._format_full_address(property_data)
            
            params = {
                'id': self.config.license_key,
                't': f"DeedsLookup_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'ff': address,  # Free form address
                'opt': f'page:{page}',
                'format': 'JSON'
            }
            
            logger.info(f"Looking up deeds for: {address}")
            
            result = await self._make_api_request('/LookupDeeds', params)
            
            if result['success']:
                data = result['data']
                records = data.get('Records', [])
                
                normalized_deeds = [self._normalize_deed_data(deed) for deed in records]
                
                return {
                    'success': True,
                    'deeds': normalized_deeds,
                    'total_records': data.get('TotalRecords', len(normalized_deeds)),
                    'page': page,
                    'source': 'melissa_deeds_api',
                    'timestamp': datetime.now().isoformat(),
                    'credits_used': 1
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error looking up deeds: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'melissa_deeds_api'
            }
    
    async def lookup_homes_by_owner(self, owner_mak: str = None, 
                                  owner_name: str = None) -> Dict[str, Any]:
        """
        Find other properties owned by the same owner
        
        Args:
            owner_mak: Melissa Address Key for the owner
            owner_name: Free form owner name search
            
        Returns:
            Dict containing properties owned by the same person/entity
        """
        try:
            params = {
                'id': self.config.license_key,
                't': f"HomesByOwner_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'format': 'JSON'
            }
            
            if owner_mak:
                params['mak'] = owner_mak
                logger.info(f"Looking up homes by owner MAK: {owner_mak}")
            elif owner_name:
                params['ff'] = owner_name
                logger.info(f"Looking up homes by owner name: {owner_name}")
            else:
                return {
                    'success': False,
                    'error': 'Either owner_mak or owner_name must be provided',
                    'source': 'melissa_homes_by_owner_api'
                }
            
            result = await self._make_api_request('/LookupHomesByOwner', params)
            
            if result['success']:
                data = result['data']
                records = data.get('Records', [])
                
                normalized_properties = [self._normalize_property_data(prop) for prop in records]
                
                return {
                    'success': True,
                    'properties': normalized_properties,
                    'total_found': len(normalized_properties),
                    'source': 'melissa_homes_by_owner_api',
                    'timestamp': datetime.now().isoformat(),
                    'credits_used': 1
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error looking up homes by owner: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'melissa_homes_by_owner_api'
            }
    
    async def _make_api_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make API request to Melissa Data with retry logic
        
        Args:
            endpoint: API endpoint path
            params: Query parameters
            
        Returns:
            Dict containing response data or error information
        """
        url = f"{self.config.base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                if not self.session:
                    raise RuntimeError("Session not initialized. Use async context manager.")
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'success': True,
                            'data': data,
                            'status_code': response.status
                        }
                    elif response.status == 401:
                        return {
                            'success': False,
                            'error': 'Invalid license key or authentication failed',
                            'status_code': response.status
                        }
                    elif response.status == 429:
                        # Rate limited - wait and retry
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'API error: {response.status} - {error_text}',
                            'status_code': response.status
                        }
                        
            except asyncio.TimeoutError:
                logger.warning(f"Request timeout on attempt {attempt + 1}")
                if attempt == self.config.max_retries - 1:
                    return {
                        'success': False,
                        'error': 'Request timeout after retries'
                    }
            except Exception as e:
                logger.error(f"Request error on attempt {attempt + 1}: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    return {
                        'success': False,
                        'error': f'Request failed: {str(e)}'
                    }
        
        return {
            'success': False,
            'error': 'Max retries exceeded'
        }
    
    def _format_full_address(self, property_data: Dict[str, Any]) -> str:
        """Format full address for free-form search"""
        address_parts = []
        
        if property_data.get('address'):
            address_parts.append(property_data['address'])
        if property_data.get('city'):
            address_parts.append(property_data['city'])
        if property_data.get('state'):
            address_parts.append(property_data['state'])
        if property_data.get('zip_code'):
            address_parts.append(str(property_data['zip_code']))
        
        return ' '.join(address_parts)
    
    def _normalize_property_data(self, property_record: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize property data to standard format"""
        
        # Extract nested data groups
        parcel = property_record.get('GrpParcel', {})
        property_address = property_record.get('GrpPropertyAddress', {})
        primary_owner = property_record.get('GrpPrimaryOwner', {})
        owner_address = property_record.get('GrpOwnerAddress', {})
        sale_info = property_record.get('GrpSaleInfo', {})
        property_size = property_record.get('GrpPropertySize', {})
        tax_info = property_record.get('GrpTax', {})
        estimated_value = property_record.get('GrpEstimatedValue', {})
        
        return {
            # Basic property info
            'address': property_address.get('AddressLine1', ''),
            'city': property_address.get('City', ''),
            'state': property_address.get('State', ''),
            'zip_code': property_address.get('PostalCode', ''),
            'county': property_address.get('County', ''),
            
            # Property characteristics
            'beds': property_size.get('BedroomsCount'),
            'baths': property_size.get('BathroomsCount'),
            'sqft': property_size.get('LivingAreaSqFt'),
            'lot_size': property_size.get('LotSizeAcres'),
            'year_built': property_size.get('YearBuilt'),
            
            # Owner information
            'owner_name': primary_owner.get('Name1Full', ''),
            'owner_address': owner_address.get('AddressLine1', ''),
            'owner_city': owner_address.get('City', ''),
            'owner_state': owner_address.get('State', ''),
            'owner_zip': owner_address.get('PostalCode', ''),
            'owner_occupied': owner_address.get('OwnerOccupied') == 'Y',
            
            # Financial information
            'assessed_value': tax_info.get('AssessedValue'),
            'market_value': tax_info.get('MarketValue'),
            'estimated_value': estimated_value.get('EstimatedValue'),
            'estimated_min_value': estimated_value.get('EstimatedMinValue'),
            'estimated_max_value': estimated_value.get('EstimatedMaxValue'),
            'confidence_score': estimated_value.get('ConfidenceScore'),
            
            # Sale information
            'last_sale_price': sale_info.get('SalePrice'),
            'last_sale_date': sale_info.get('SaleDate'),
            
            # Tax information
            'tax_amount': tax_info.get('TaxAmount'),
            'tax_year': tax_info.get('TaxYear'),
            
            # Identifiers
            'parcel_id': parcel.get('ParcelID', ''),
            'mak': parcel.get('MAK', ''),  # Melissa Address Key
            
            # Metadata
            'source': 'melissa_property_api',
            'data_quality': 'high',
            'raw_data': property_record
        }
    
    def _normalize_deed_data(self, deed_record: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize deed data to standard format"""
        
        doc_info = deed_record.get('GrpDocInfo', {})
        tx_def_info = deed_record.get('GrpTxDefInfo', {})
        tx_amt_info = deed_record.get('GrpTxAmtInfo', {})
        primary_grantor = deed_record.get('GrpPrimaryGrantor', {})
        primary_grantee = deed_record.get('GrpPrimaryGrantee', {})
        
        return {
            # Document information
            'document_type': doc_info.get('TypeCode', ''),
            'recording_date': doc_info.get('RecordingDate', ''),
            'document_number': doc_info.get('DocumentNumber', ''),
            
            # Transaction information
            'transaction_type': tx_def_info.get('TransactionType', ''),
            'sale_price': tx_amt_info.get('SalePrice'),
            'transfer_amount': tx_amt_info.get('TransferAmount'),
            
            # Parties involved
            'grantor_name': primary_grantor.get('Name1Full', ''),  # Seller
            'grantee_name': primary_grantee.get('Name1Full', ''),  # Buyer
            
            # Flags
            'arms_length': tx_def_info.get('ArmsLengthFlag') == 'Y',
            'quit_claim': tx_def_info.get('QuitClaimFlag') == 'Y',
            
            # Metadata
            'source': 'melissa_deeds_api',
            'raw_data': deed_record
        }

# Utility functions for easy integration
async def get_melissa_property_data(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get comprehensive property data from Melissa Data API
    
    Args:
        property_data: Property information including address
        
    Returns:
        Dict containing property details
    """
    async with MelissaAgent() as agent:
        return await agent.lookup_property(property_data)

async def get_melissa_deed_history(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get historical deed records from Melissa Data API
    
    Args:
        property_data: Property information including address
        
    Returns:
        Dict containing deed history
    """
    async with MelissaAgent() as agent:
        return await agent.lookup_deeds(property_data)

async def get_melissa_owner_properties(owner_name: str) -> Dict[str, Any]:
    """
    Find other properties owned by the same owner
    
    Args:
        owner_name: Owner name to search for
        
    Returns:
        Dict containing properties owned by the same person
    """
    async with MelissaAgent() as agent:
        return await agent.lookup_homes_by_owner(owner_name=owner_name)
