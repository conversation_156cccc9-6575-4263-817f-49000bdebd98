#!/usr/bin/env python3
"""
Realie.ai Agent - Premium Property Data and Comparables API Integration
Provides comprehensive property data with 180+ million properties and 100+ data fields
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from agents.agent_base import Agent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class RealieConfig:
    """Configuration for Realie.ai API"""
    api_key: str
    base_url: str = "https://app.realie.ai/api/public"
    timeout: int = 30
    max_retries: int = 3
    rate_limit_per_minute: int = 1200

class RealieAgent(Agent):
    """
    Realie.ai Agent for premium property data and comparables
    
    Features:
    - 180+ million properties with 100+ data fields
    - County-sourced data for maximum accuracy
    - Premium comparables search with advanced filtering
    - AI-powered property analysis
    - Lightning-fast API with low latency
    """
    
    def __init__(self, lead_id: str = "realie_agent", entity: str = "property_data"):
        super().__init__("realie_agent", lead_id, entity)
        
        # Initialize Realie configuration
        api_key = os.getenv('REALIE_API_KEY')
        if not api_key:
            raise ValueError("REALIE_API_KEY environment variable is required")
        
        self.config = RealieConfig(api_key=api_key)
        self.session = None
        
        logger.info("Realie.ai Agent initialized successfully")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            headers={'Authorization': self.config.api_key}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def run(self):
        """Required by Agent base class - not used for async operations"""
        pass
    
    async def get_premium_comparables(self, property_data: Dict[str, Any], 
                                    radius_miles: float = 1.0, 
                                    time_frame_months: int = 18,
                                    max_results: int = 25) -> Dict[str, Any]:
        """
        Get premium comparable properties using Realie's advanced comparables search
        
        Args:
            property_data: Property information including lat/lng or address
            radius_miles: Search radius in miles (default 1.0)
            time_frame_months: Months to look back for sales (default 18)
            max_results: Maximum results to return (max 50)
            
        Returns:
            Dict containing success status, comparables data, and metadata
        """
        try:
            # Get coordinates for the property
            lat, lng = await self._get_coordinates(property_data)
            if not lat or not lng:
                return {
                    'success': False,
                    'error': 'Could not determine property coordinates',
                    'source': 'realie_api'
                }
            
            # Build search parameters
            params = {
                'latitude': lat,
                'longitude': lng,
                'radius': min(radius_miles, 10),  # Reasonable max radius
                'timeFrame': min(time_frame_months, 36),  # Max 3 years
                'maxResults': min(max_results, 50)  # API max is 50
            }
            
            # Add property filters if available
            if property_data.get('sqft'):
                sqft = property_data['sqft']
                # Allow 20% variance in square footage
                params['sqftMin'] = int(sqft * 0.8)
                params['sqftMax'] = int(sqft * 1.2)
            
            if property_data.get('beds'):
                beds = property_data['beds']
                params['bedsMin'] = max(1, beds - 1)
                params['bedsMax'] = beds + 1
            
            if property_data.get('baths'):
                baths = property_data['baths']
                params['bathsMin'] = max(1, baths - 0.5)
                params['bathsMax'] = baths + 0.5
            
            # Add property type filter
            property_type = self._normalize_property_type(property_data.get('property_type', ''))
            if property_type:
                params['propertyType'] = property_type
            
            logger.info(f"Getting premium comparables for lat/lng: {lat}, {lng}")
            
            result = await self._make_api_request('/premium/comparables/', params)
            
            if result['success']:
                data = result['data']
                comparables = data.get('comparables', [])
                metadata = data.get('metadata', {})
                
                # Normalize the comparables data
                normalized_comps = self._normalize_comparables(comparables)
                
                return {
                    'success': True,
                    'comps': normalized_comps,
                    'total_found': metadata.get('count', len(normalized_comps)),
                    'search_params': params,
                    'source': 'realie_premium_api',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error getting premium comparables: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'realie_premium_api'
            }
    
    async def get_property_by_address(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed property information by address
        
        Args:
            property_data: Property information including address
            
        Returns:
            Dict containing property details
        """
        try:
            address = self._format_address(property_data)
            
            params = {'address': address}
            
            logger.info(f"Getting property details for address: {address}")
            
            result = await self._make_api_request('/address/', params)
            
            if result['success']:
                data = result['data']
                
                # Normalize property data
                normalized_property = self._normalize_property_data(data)
                
                return {
                    'success': True,
                    'property': normalized_property,
                    'source': 'realie_address_api',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error getting property by address: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'realie_address_api'
            }
    
    async def search_properties_by_location(self, lat: float, lng: float, 
                                          radius_miles: float = 1.0,
                                          max_results: int = 25) -> Dict[str, Any]:
        """
        Search properties by geographic location
        
        Args:
            lat: Latitude
            lng: Longitude
            radius_miles: Search radius in miles
            max_results: Maximum results to return
            
        Returns:
            Dict containing properties in the area
        """
        try:
            params = {
                'latitude': lat,
                'longitude': lng,
                'radius': radius_miles,
                'limit': min(max_results, 100)  # Reasonable limit
            }
            
            logger.info(f"Searching properties near lat/lng: {lat}, {lng}")
            
            result = await self._make_api_request('/location/', params)
            
            if result['success']:
                data = result['data']
                properties = data.get('properties', [])
                
                # Normalize properties
                normalized_properties = [self._normalize_property_data(prop) for prop in properties]
                
                return {
                    'success': True,
                    'properties': normalized_properties,
                    'total_found': len(normalized_properties),
                    'search_params': params,
                    'source': 'realie_location_api',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error searching properties by location: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'source': 'realie_location_api'
            }
    
    async def _get_coordinates(self, property_data: Dict[str, Any]) -> tuple:
        """Get latitude and longitude for a property"""
        # Check if coordinates are already provided
        if property_data.get('latitude') and property_data.get('longitude'):
            return property_data['latitude'], property_data['longitude']
        
        # Try to get coordinates using address lookup
        if property_data.get('address'):
            address_result = await self.get_property_by_address(property_data)
            if address_result.get('success'):
                prop = address_result.get('property', {})
                return prop.get('latitude'), prop.get('longitude')
        
        return None, None
    
    async def _make_api_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make API request to Realie with retry logic
        
        Args:
            endpoint: API endpoint path
            params: Query parameters
            
        Returns:
            Dict containing response data or error information
        """
        url = f"{self.config.base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                if not self.session:
                    raise RuntimeError("Session not initialized. Use async context manager.")
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'success': True,
                            'data': data,
                            'status_code': response.status
                        }
                    elif response.status == 401:
                        return {
                            'success': False,
                            'error': 'Invalid API key or authentication failed',
                            'status_code': response.status
                        }
                    elif response.status == 429:
                        # Rate limited - wait and retry
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'API error: {response.status} - {error_text}',
                            'status_code': response.status
                        }
                        
            except asyncio.TimeoutError:
                logger.warning(f"Request timeout on attempt {attempt + 1}")
                if attempt == self.config.max_retries - 1:
                    return {
                        'success': False,
                        'error': 'Request timeout after retries'
                    }
            except Exception as e:
                logger.error(f"Request error on attempt {attempt + 1}: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    return {
                        'success': False,
                        'error': f'Request failed: {str(e)}'
                    }
        
        return {
            'success': False,
            'error': 'Max retries exceeded'
        }
    
    def _format_address(self, property_data: Dict[str, Any]) -> str:
        """Format address for API request"""
        address_parts = []
        
        if property_data.get('address'):
            address_parts.append(property_data['address'])
        if property_data.get('city'):
            address_parts.append(property_data['city'])
        if property_data.get('state'):
            address_parts.append(property_data['state'])
        if property_data.get('zip_code'):
            address_parts.append(str(property_data['zip_code']))
        
        return ', '.join(address_parts)
    
    def _normalize_property_type(self, property_type: str) -> str:
        """Normalize property type for Realie API"""
        if not property_type:
            return 'any'
        
        property_type = property_type.lower()
        
        # Map to Realie's property types: any, condo, house
        if 'condo' in property_type or 'condominium' in property_type:
            return 'condo'
        elif any(term in property_type for term in ['single', 'house', 'family', 'sfh']):
            return 'house'
        else:
            return 'any'
    
    def _normalize_comparables(self, comparables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize comparables data to standard format"""
        normalized = []
        
        for comp in comparables:
            normalized_comp = {
                'address': comp.get('address', ''),
                'city': comp.get('city', ''),
                'state': comp.get('state', ''),
                'zip_code': comp.get('zipCode', ''),
                'latitude': comp.get('latitude'),
                'longitude': comp.get('longitude'),
                'sale_price': comp.get('salePrice'),
                'sale_date': comp.get('saleDate'),
                'beds': comp.get('bedrooms'),
                'baths': comp.get('bathrooms'),
                'sqft': comp.get('squareFootage'),
                'lot_size': comp.get('lotSize'),
                'year_built': comp.get('yearBuilt'),
                'property_type': comp.get('propertyType', '').lower(),
                'assessed_value': comp.get('assessedValue'),
                'market_value': comp.get('marketValue'),
                'source': 'realie_premium_api',
                'confidence_score': 0.9,  # Realie has very high-quality county data
                'raw_data': comp  # Keep original data for debugging
            }
            
            normalized.append(normalized_comp)
        
        return normalized
    
    def _normalize_property_data(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize property data to standard format"""
        return {
            'address': property_data.get('address', ''),
            'city': property_data.get('city', ''),
            'state': property_data.get('state', ''),
            'zip_code': property_data.get('zipCode', ''),
            'latitude': property_data.get('latitude'),
            'longitude': property_data.get('longitude'),
            'beds': property_data.get('bedrooms'),
            'baths': property_data.get('bathrooms'),
            'sqft': property_data.get('squareFootage'),
            'lot_size': property_data.get('lotSize'),
            'year_built': property_data.get('yearBuilt'),
            'property_type': property_data.get('propertyType', '').lower(),
            'assessed_value': property_data.get('assessedValue'),
            'market_value': property_data.get('marketValue'),
            'owner_name': property_data.get('ownerName'),
            'parcel_id': property_data.get('parcelId'),
            'source': 'realie_api',
            'raw_data': property_data
        }

# Utility functions for easy integration
async def get_realie_comparables(property_data: Dict[str, Any], 
                               radius_miles: float = 1.0,
                               time_frame_months: int = 18,
                               max_results: int = 25) -> Dict[str, Any]:
    """
    Get premium comparables from Realie.ai API
    
    Args:
        property_data: Property information
        radius_miles: Search radius in miles
        time_frame_months: Months to look back for sales
        max_results: Maximum results to return
        
    Returns:
        Dict containing comparables data
    """
    async with RealieAgent() as agent:
        return await agent.get_premium_comparables(
            property_data, radius_miles, time_frame_months, max_results
        )

async def get_realie_property_details(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get detailed property information from Realie.ai
    
    Args:
        property_data: Property information including address
        
    Returns:
        Dict containing property details
    """
    async with RealieAgent() as agent:
        return await agent.get_property_by_address(property_data)
