import os
import logging
import json
import datetime
from typing import Dict, Any, List, Optional
import requests
from agents.mao_calculator import MAOCalculator
from agents.workflows.comping_workflow import run_comping_workflow
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OfferGenerator:
    """
    Generates property offers by combining comping workflow and MAO calculator.
    """
    
    def __init__(self):
        """Initialize the offer generator."""
        self.mao_calculator = MAOCalculator()
        self.offer_template = self._load_offer_template()
    
    def _load_offer_template(self) -> str:
        """
        Load the offer template from the prompt templates.
        
        Returns:
            str: The offer template text
        """
        try:
            template_path = "prompt_templates/mao_offer.md"
            with open(template_path, "r") as f:
                template = f.read()
            return template
        except Exception as e:
            logger.error(f"Error loading offer template: {str(e)}")
            # Return a basic template as fallback
            return """
            # Property Offer

            Based on our analysis, we're offering **${mao}** for your property at ${address}.
            
            This offer is based on:
            - After Repair Value (ARV): ${arv}
            - Estimated repair costs: ${repair_cost}
            - Closing costs and other expenses
            
            This offer is valid for 7 days from ${offer_date}.
            """
    
    async def generate_offer(self, property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an offer for a property by running the comping workflow and MAO calculation.
        
        Args:
            property_data (Dict[str, Any]): Property details
            
        Returns:
            Dict[str, Any]: Offer details and message
        """
        logger.info(f"Generating offer for property at {property_data.get('address')}")
        
        # Step 1: Run the comping workflow to get comps and ARV
        try:
            comps_result = await run_comping_workflow(property_data)
            
            if not comps_result.get("success", False) and not comps_result.get("comps", []):
                error_msg = f"Failed to get comps for property: {comps_result.get('error', 'Unknown error')}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "property_id": property_data.get("id", ""),
                    "property_address": property_data.get("address", "")
                }
        except Exception as e:
            error_msg = f"Error running comping workflow: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "property_id": property_data.get("id", ""),
                "property_address": property_data.get("address", "")
            }
        
        # Step 2: Generate offer using MAO calculator
        try:
            offer_details = self.mao_calculator.generate_offer_from_comps(property_data, comps_result)
            
            if not offer_details.get("success", False):
                return offer_details
        except Exception as e:
            error_msg = f"Error generating offer: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "property_id": property_data.get("id", ""),
                "property_address": property_data.get("address", "")
            }
        
        # Step 3: Generate offer message from template
        try:
            offer_message = self._generate_offer_message(property_data, offer_details)
            offer_details["offer_message"] = offer_message
        except Exception as e:
            logger.error(f"Error generating offer message: {str(e)}")
            offer_details["offer_message"] = f"We'd like to offer ${offer_details.get('mao', 0):,.2f} for your property."
        
        # Step 4: Store the offer in the database
        try:
            self._store_offer(offer_details)
        except Exception as e:
            logger.warning(f"Error storing offer: {str(e)}")
        
        return offer_details
    
    def _generate_offer_message(self, property_data: Dict[str, Any], offer_details: Dict[str, Any]) -> str:
        """Generate offer message based on template and data"""
        template = self._load_offer_template()
        
        # Check if this is a realtor-sourced lead
        is_realtor_lead = property_data.get("source_type") == "realtor"
        
        if is_realtor_lead:
            # Add realtor-specific content
            template = self._load_realtor_offer_template()
            
            # Include realtor commission details if applicable
            if property_data.get("include_commission", True):
                offer_details["commission_note"] = "This offer includes your standard commission."
        
        # Format template with property and offer details
        return self._format_template(template, property_data, offer_details)
    
    def _store_offer(self, offer_details: Dict[str, Any]) -> bool:
        """
        Store the offer in the database.
        
        Args:
            offer_details (Dict[str, Any]): Offer details
            
        Returns:
            bool: True if successful, False otherwise
        """
        # For MVP, we'll just log the offer
        logger.info(f"Storing offer for property {offer_details.get('property_address')}: ${offer_details.get('mao', 0):,.2f}")
        
        # In a real implementation, this would store the offer in a database
        # For now, we'll return True to indicate success
        return True


# Helper function to generate an offer for a property
async def generate_property_offer(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate an offer for a property.
    
    Args:
        property_data (Dict[str, Any]): Property details
        
    Returns:
        Dict[str, Any]: Offer details and message
    """
    generator = OfferGenerator()
    return await generator.generate_offer(property_data)
