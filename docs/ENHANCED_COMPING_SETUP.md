# Enhanced Comping System Setup Guide

## Overview

The enhanced comping system solves the challenge of efficiently obtaining property comparables from platforms like Privy, BatchLeads, and Lotside without relying solely on web scraping. It uses a **hybrid approach**:

1. **API-First Strategy**: Fast, reliable data from property APIs
2. **Session-Managed Scraping**: Efficient fallback when APIs are insufficient
3. **Smart Orchestration**: Automatically chooses the best approach based on cost and speed requirements

## Architecture

```
Property Request → Smart Orchestrator → API Sources (Primary) → Scraping (Fallback) → Normalized Results
```

### Components

- **SmartCompingOrchestrator**: Routes requests to optimal data sources
- **EnhancedCompingService**: Combines multiple API sources
- **SessionManagedScraper**: Efficient scraping with persistent sessions
- **CompingWorkflow**: Legacy workflow with enhanced integration

## Setup Instructions

### 1. API Sources (Recommended)

#### RealtyMole API (Primary Recommendation)
- **Cost**: $29/month for 1000 requests (~$0.029 per property)
- **Coverage**: Nationwide US
- **Data Quality**: High
- **Setup**:
  1. Sign up at https://realtymole.com/api
  2. Get your API key
  3. Add to `.env`: `REALTY_MOLE_API_KEY=your_key_here`

#### SmartyStreets API (Address Validation)
- **Cost**: $0.30 per lookup
- **Coverage**: Nationwide US
- **Data Quality**: Very High
- **Setup**:
  1. Sign up at https://www.smartystreets.com/
  2. Get Auth ID and Token
  3. Add to `.env`:
     ```
     SMARTY_STREETS_AUTH_ID=your_auth_id
     SMARTY_STREETS_AUTH_TOKEN=your_auth_token
     ```

#### ATTOM Data API (Enterprise)
- **Cost**: Contact for pricing (typically $500+/month)
- **Coverage**: Nationwide US
- **Data Quality**: Very High
- **Setup**:
  1. Contact ATTOM Data for enterprise access
  2. Add to `.env`: `ATTOM_DATA_API_KEY=your_key_here`

### 2. Scraper Credentials (Fallback)

For when APIs don't have sufficient data:

```bash
# BatchLeads (Most Reliable)
SCRAPER_BATCHLEADS_EMAIL=<EMAIL>
SCRAPER_BATCHLEADS_PASSWORD=your_password

# Privy (Secondary)
SCRAPER_PRIVY_EMAIL=<EMAIL>
SCRAPER_PRIVY_PASSWORD=your_password

# Lotside (Michigan Focus)
SCRAPER_LOTSIDE_EMAIL=<EMAIL>
SCRAPER_LOTSIDE_PASSWORD=your_password
```

### 3. Configuration

Update `config/workflows.json` to enable the enhanced system:

```json
{
  "comping": {
    "enabled": true,
    "strategy": "cost_optimized",
    "sources": {
      "realty_mole_api": {
        "priority": 1,
        "enabled": true,
        "weight": 0.9
      },
      "batchleads_scraper": {
        "priority": 4,
        "enabled": true,
        "weight": 0.6,
        "session_management": true
      }
    }
  }
}
```

## Usage

### Basic Usage

```python
from agents.workflows.comping_workflow import run_enhanced_comping_workflow

property_data = {
    "address": "123 Main St",
    "city": "Detroit",
    "state": "MI",
    "zip_code": "48201",
    "estimated_value": 150000
}

# Get comps using smart orchestration
result = await run_enhanced_comping_workflow(property_data, "cost_optimized")

if result['success']:
    comps = result['comps']
    print(f"Found {len(comps)} comparable properties")
```

### Strategy Options

1. **"speed_first"**: API-only, fastest results
2. **"cost_optimized"**: Balance of speed and cost (recommended)
3. **"comprehensive"**: Use all available sources
4. **"scraping_only"**: For testing or when APIs unavailable

### Batch Processing

```python
from agents.workflows.smart_comping_orchestrator import SmartCompingOrchestrator

orchestrator = SmartCompingOrchestrator()
properties = [property1, property2, property3]

results = await orchestrator.batch_process_properties(properties, "cost_optimized")
```

## Testing

Run the test suite to verify your setup:

```bash
python scripts/test_enhanced_comping.py
```

This will:
- Check API availability
- Test different strategies
- Show performance metrics
- Provide setup recommendations

## Cost Analysis

### API Costs (per property)
- RealtyMole: $0.029
- SmartyStreets: $0.30
- ATTOM Data: ~$0.50

### Scraping Costs (per property)
- Time: ~2 minutes
- Infrastructure: ~$0.01
- Success Rate: 60-75%

### Recommended Strategy
For most use cases, use **"cost_optimized"** strategy:
1. Try RealtyMole API first ($0.029)
2. If insufficient data, use session-managed BatchLeads scraping
3. Average cost: ~$0.05 per property with 90%+ success rate

## Performance Optimization

### Session Management
The system maintains persistent browser sessions to reduce login overhead:
- Sessions last up to 2 hours
- Automatic session recovery
- Shared sessions across multiple requests

### Caching
Results are cached for 24 hours to avoid duplicate requests:
- Cache key based on property address
- Automatic cache invalidation
- Reduces API costs and improves speed

### Rate Limiting
Built-in rate limiting to respect API and website limits:
- API: Configurable per-source limits
- Scraping: 2-second delays between requests
- Automatic backoff on errors

## Troubleshooting

### Common Issues

1. **"No API key" errors**
   - Verify API keys are set in `.env`
   - Check key validity and account status

2. **Scraping failures**
   - Verify credentials are correct
   - Check if websites have changed their structure
   - Review browser automation logs

3. **Insufficient comps**
   - Try "comprehensive" strategy
   - Check property address formatting
   - Verify location is covered by data sources

### Monitoring

Check performance with:

```python
orchestrator = SmartCompingOrchestrator()
stats = orchestrator.get_performance_stats()
print(f"API success rate: {stats['api_success_rate']:.2%}")
print(f"Average cost: ${stats['cost_per_property']:.2f}")
```

## Integration with Lead Workflow

The enhanced comping system integrates seamlessly with your existing lead workflow:

```python
# In your lead processing workflow
if lead_tier == 1:  # High-value leads
    comps_result = await run_enhanced_comping_workflow(property_data, "comprehensive")
else:  # Standard leads
    comps_result = await run_enhanced_comping_workflow(property_data, "cost_optimized")

# Use comps for MAO calculation
if comps_result['success']:
    arv = calculate_arv_from_comps(comps_result['comps'])
    mao = calculate_mao(arv, repair_costs, profit_margin)
```

## Next Steps

1. **Set up API keys** for RealtyMole (minimum recommended)
2. **Configure scraper credentials** for BatchLeads fallback
3. **Test the system** with your property data
4. **Monitor performance** and adjust strategies as needed
5. **Scale up** by adding more API sources as volume increases

## Support

For issues or questions:
1. Check the test script output for diagnostics
2. Review logs in `logs/` directory
3. Verify configuration in `config/` files
4. Test individual components separately
