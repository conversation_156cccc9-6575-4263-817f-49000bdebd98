#!/usr/bin/env python3
"""
Test script for Multi-Tier Robust Comping System
Tests the integration of Realie.ai + RentCast + Scraping fallback
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.workflows.multi_tier_comping_orchestrator import MultiTierCompingOrchestrator, get_multi_tier_comps
from agents.realie_agent import get_realie_comparables, get_realie_property_details
from agents.rentcast_agent import get_rentcast_comps, get_rentcast_value_estimate

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test properties
TEST_PROPERTIES = [
    {
        "address": "123 Main St",
        "city": "Detroit",
        "state": "MI",
        "zip_code": "48201",
        "beds": 3,
        "baths": 1,
        "sqft": 1200,
        "estimated_value": 75000
    },
    {
        "address": "456 Oak Ave",
        "city": "Birmingham",
        "state": "MI", 
        "zip_code": "48009",
        "beds": 4,
        "baths": 3,
        "sqft": 2500,
        "estimated_value": 450000
    },
    {
        "address": "789 Pine Rd",
        "city": "Royal Oak",
        "state": "MI",
        "zip_code": "48067",
        "beds": 3,
        "baths": 2,
        "sqft": 1800,
        "estimated_value": 250000
    }
]

async def test_api_availability():
    """Test which APIs are available"""
    print("🔌 Testing Multi-Tier API Availability")
    print("=" * 60)
    
    api_keys = {
        "Realie.ai": os.getenv('REALIE_API_KEY'),
        "RentCast": os.getenv('RENTCAST_API_KEY'),
        "SmartyStreets": os.getenv('SMARTY_STREETS_AUTH_ID') and os.getenv('SMARTY_STREETS_AUTH_TOKEN')
    }
    
    available_apis = []
    for api_name, has_key in api_keys.items():
        status = "✅ Available" if has_key else "❌ Missing API Key"
        print(f"   {api_name}: {status}")
        if has_key:
            available_apis.append(api_name)
    
    print(f"\n📊 Available APIs: {len(available_apis)}/3")
    
    if len(available_apis) == 0:
        print("   ⚠️  No APIs available - will use scraping only")
    elif len(available_apis) == 1:
        print("   ⚡ Single API available - limited functionality")
    elif len(available_apis) == 2:
        print("   🚀 Two APIs available - good coverage")
    else:
        print("   🎯 All APIs available - maximum robustness")
    
    return available_apis

async def test_individual_apis():
    """Test individual API sources"""
    print("\n🧪 Testing Individual API Sources")
    print("=" * 60)
    
    test_property = TEST_PROPERTIES[0]
    print(f"📍 Test Property: {test_property['address']}, {test_property['city']}, {test_property['state']}")
    
    # Test Realie.ai
    if os.getenv('REALIE_API_KEY'):
        print(f"\n🏢 Testing Realie.ai API")
        try:
            start_time = datetime.now()
            result = await get_realie_comparables(test_property, radius_miles=1.0, max_results=5)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comps = result.get('comps', [])
                print(f"   ✅ Success: {len(comps)} comps in {processing_time:.2f}s")
                if comps:
                    first_comp = comps[0]
                    print(f"   🏠 Example: {first_comp.get('address', 'N/A')} - ${first_comp.get('sale_price', 'N/A'):,}" if first_comp.get('sale_price') else "   🏠 Example: No sale price")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
    
    # Test RentCast
    if os.getenv('RENTCAST_API_KEY'):
        print(f"\n🏘️  Testing RentCast API")
        try:
            start_time = datetime.now()
            result = await get_rentcast_comps(test_property, radius_miles=1.0, limit=5)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comps = result.get('comps', [])
                print(f"   ✅ Success: {len(comps)} comps in {processing_time:.2f}s")
                if comps:
                    first_comp = comps[0]
                    print(f"   🏠 Example: {first_comp.get('address', 'N/A')} - ${first_comp.get('sale_price', 'N/A'):,}" if first_comp.get('sale_price') else "   🏠 Example: No sale price")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"   💥 Error: {str(e)}")

async def test_multi_tier_strategies():
    """Test different multi-tier strategies"""
    print("\n🎯 Testing Multi-Tier Strategies")
    print("=" * 60)
    
    strategies = ["premium", "balanced", "cost_optimized", "speed_first"]
    test_property = TEST_PROPERTIES[1]  # Use Birmingham property
    
    print(f"📍 Test Property: {test_property['address']}, {test_property['city']}, {test_property['state']}")
    print(f"💰 Estimated Value: ${test_property['estimated_value']:,}")
    
    for strategy in strategies:
        print(f"\n🔄 Strategy: {strategy.upper()}")
        
        try:
            start_time = datetime.now()
            result = await get_multi_tier_comps(test_property, strategy)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comps = result.get('comps', [])
                sources_used = result.get('sources_used', [])
                arv_estimate = result.get('arv_estimate')
                confidence_score = result.get('confidence_score', 0)
                
                print(f"   ✅ Success: {len(comps)} comps in {processing_time:.2f}s")
                print(f"   📊 Sources: {', '.join(sources_used)}")
                print(f"   💰 ARV Estimate: ${arv_estimate:,.0f}" if arv_estimate else "   💰 ARV Estimate: N/A")
                print(f"   📈 Confidence: {confidence_score:.1%}")
                
                # Show performance metrics
                if 'performance' in result:
                    perf = result['performance']
                    stats = perf.get('stats', {})
                    print(f"   🔧 API Calls: Realie({stats.get('realie_calls', 0)}) + RentCast({stats.get('rentcast_calls', 0)}) + Scraping({stats.get('scraping_calls', 0)})")
                    print(f"   💸 Estimated Cost: ${stats.get('total_cost', 0):.2f}")
                
                # Show top comp
                if comps:
                    top_comp = comps[0]
                    print(f"   🏆 Top Comp: {top_comp.get('address', 'N/A')} - ${top_comp.get('sale_price', 'N/A'):,}" if top_comp.get('sale_price') else "   🏆 Top Comp: No sale price")
                    print(f"   📊 Quality Score: {top_comp.get('quality_score', 0):.2f}")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
        
        print("-" * 40)

async def test_batch_processing():
    """Test batch processing with multiple properties"""
    print("\n📦 Testing Batch Processing")
    print("=" * 60)
    
    orchestrator = MultiTierCompingOrchestrator()
    
    try:
        start_time = datetime.now()
        
        results = []
        for i, property_data in enumerate(TEST_PROPERTIES):
            print(f"\n📍 Processing Property {i+1}/{len(TEST_PROPERTIES)}: {property_data['address']}")
            
            result = await orchestrator.get_comprehensive_comps(property_data, "balanced")
            results.append({
                'property': property_data,
                'result': result
            })
            
            if result.get('success'):
                comps_count = len(result.get('comps', []))
                sources = ', '.join(result.get('sources_used', []))
                print(f"   ✅ {comps_count} comps from: {sources}")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # Summary statistics
        successful_results = [r for r in results if r['result'].get('success')]
        total_comps = sum(len(r['result'].get('comps', [])) for r in successful_results)
        
        print(f"\n📊 Batch Processing Summary:")
        print(f"   ⏱️  Total time: {total_time:.2f}s")
        print(f"   📈 Success rate: {len(successful_results)}/{len(results)} ({len(successful_results)/len(results):.1%})")
        print(f"   🏠 Total comps found: {total_comps}")
        print(f"   ⚡ Average time per property: {total_time/len(results):.2f}s")
        
        # Performance stats
        stats = orchestrator.get_performance_stats()
        print(f"   💰 Total estimated cost: ${stats['stats']['total_cost']:.2f}")
        print(f"   📊 Cost per property: ${stats['cost_per_property']:.2f}")
        
    except Exception as e:
        print(f"   💥 Batch processing error: {str(e)}")

async def test_data_quality_comparison():
    """Compare data quality across different sources"""
    print("\n🔬 Testing Data Quality Comparison")
    print("=" * 60)
    
    test_property = TEST_PROPERTIES[2]  # Royal Oak property
    print(f"📍 Test Property: {test_property['address']}, {test_property['city']}, {test_property['state']}")
    
    # Get results from premium strategy (all sources)
    try:
        result = await get_multi_tier_comps(test_property, "premium")
        
        if result.get('success'):
            comps = result.get('comps', [])
            
            # Analyze by source
            source_analysis = {}
            for comp in comps:
                source = comp.get('source', 'unknown')
                if source not in source_analysis:
                    source_analysis[source] = {
                        'count': 0,
                        'avg_quality': 0,
                        'price_range': []
                    }
                
                source_analysis[source]['count'] += 1
                source_analysis[source]['avg_quality'] += comp.get('quality_score', 0)
                if comp.get('sale_price'):
                    source_analysis[source]['price_range'].append(comp['sale_price'])
            
            # Calculate averages
            for source, data in source_analysis.items():
                if data['count'] > 0:
                    data['avg_quality'] /= data['count']
                    if data['price_range']:
                        data['avg_price'] = sum(data['price_range']) / len(data['price_range'])
                        data['price_std'] = (sum((p - data['avg_price'])**2 for p in data['price_range']) / len(data['price_range']))**0.5
            
            print(f"\n📊 Data Quality Analysis:")
            for source, data in source_analysis.items():
                print(f"\n   🔍 {source.upper()}:")
                print(f"      Count: {data['count']} comps")
                print(f"      Avg Quality Score: {data['avg_quality']:.2f}")
                if 'avg_price' in data:
                    print(f"      Avg Price: ${data['avg_price']:,.0f}")
                    print(f"      Price Std Dev: ${data['price_std']:,.0f}")
            
            # Overall metrics
            arv = result.get('arv_estimate')
            confidence = result.get('confidence_score', 0)
            value_estimate = result.get('value_estimate')
            
            print(f"\n🎯 Overall Analysis:")
            print(f"   💰 ARV Estimate: ${arv:,.0f}" if arv else "   💰 ARV Estimate: N/A")
            print(f"   🤖 RentCast Value: ${value_estimate:,.0f}" if value_estimate else "   🤖 RentCast Value: N/A")
            print(f"   📈 Confidence Score: {confidence:.1%}")
            
            if arv and value_estimate:
                variance = abs(arv - value_estimate) / value_estimate
                print(f"   📊 ARV vs Value Variance: {variance:.1%}")
        
        else:
            print(f"   ❌ Failed to get premium data: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"   💥 Error: {str(e)}")

def print_setup_guide():
    """Print setup guide for multi-tier system"""
    print("\n📋 Multi-Tier Comping Setup Guide")
    print("=" * 60)
    
    print("\n🎯 Recommended Setup Order:")
    print("1. 🆓 Start with RentCast (free tier: 50 requests/month)")
    print("   • Visit: https://app.rentcast.io/app/api")
    print("   • Add: RENTCAST_API_KEY=your_key")
    
    print("\n2. 🚀 Add Realie.ai for premium data (free tier: 25 requests/month)")
    print("   • Visit: https://app.realie.ai/developer")
    print("   • Add: REALIE_API_KEY=your_key")
    
    print("\n3. 🔧 Configure scraper fallback (optional)")
    print("   • Add BatchLeads credentials for edge cases")
    
    print("\n💰 Cost Comparison (per property):")
    print("   • Realie Tier 1: $0.04 (1,250 requests/month)")
    print("   • RentCast Starter: $0.08 (500 requests/month)")
    print("   • Scraping fallback: ~$2.00 (time cost)")
    
    print("\n🎯 Strategy Recommendations:")
    print("   • Testing: Use 'balanced' strategy")
    print("   • Production: Use 'premium' for high-value properties")
    print("   • High volume: Use 'cost_optimized'")
    print("   • Real-time: Use 'speed_first'")

async def main():
    """Main test function"""
    
    print("🚀 Multi-Tier Robust Comping System Test Suite")
    print("=" * 70)
    
    # Test API availability
    available_apis = await test_api_availability()
    
    if len(available_apis) == 0:
        print_setup_guide()
        return
    
    # Test individual APIs
    await test_individual_apis()
    
    # Test multi-tier strategies
    await test_multi_tier_strategies()
    
    # Test batch processing
    await test_batch_processing()
    
    # Test data quality comparison
    await test_data_quality_comparison()
    
    print("\n✨ Multi-Tier Test Suite Completed!")
    print("\n💡 Next Steps:")
    print("   1. Set up missing API keys for full functionality")
    print("   2. Choose optimal strategy for your use case")
    print("   3. Integrate with your lead processing workflow")
    print("   4. Monitor costs and performance in production")
    
    if len(available_apis) < 3:
        print("\n🔧 To unlock full potential:")
        print("   • Get both Realie.ai AND RentCast API keys")
        print("   • This gives you the most robust comping system available")

if __name__ == "__main__":
    asyncio.run(main())
