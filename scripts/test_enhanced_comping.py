#!/usr/bin/env python3
"""
Test script for the enhanced comping system
Demonstrates the new API-first approach with selective scraping fallback
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.workflows.smart_comping_orchestrator import SmartCompingOrchestrator
from agents.workflows.comping_workflow import run_enhanced_comping_workflow

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test properties
TEST_PROPERTIES = [
    {
        "address": "123 Main St",
        "city": "Detroit",
        "state": "MI",
        "zip_code": "48201",
        "estimated_value": 75000,
        "sqft": 1200,
        "beds": 3,
        "baths": 1
    },
    {
        "address": "456 Oak Ave",
        "city": "Birmingham",
        "state": "MI", 
        "zip_code": "48009",
        "estimated_value": 450000,
        "sqft": 2500,
        "beds": 4,
        "baths": 3
    },
    {
        "address": "789 Pine Rd",
        "city": "Royal Oak",
        "state": "MI",
        "zip_code": "48067",
        "estimated_value": 250000,
        "sqft": 1800,
        "beds": 3,
        "baths": 2
    }
]

async def test_smart_orchestrator():
    """Test the smart comping orchestrator with different strategies"""
    
    print("🧪 Testing Smart Comping Orchestrator")
    print("=" * 50)
    
    orchestrator = SmartCompingOrchestrator()
    
    for i, property_data in enumerate(TEST_PROPERTIES):
        print(f"\n📍 Testing Property {i+1}: {property_data['address']}")
        print(f"   Value: ${property_data['estimated_value']:,}")
        
        # Test different strategies
        strategies = ["speed_first", "cost_optimized", "comprehensive"]
        
        for strategy in strategies:
            print(f"\n   🔄 Strategy: {strategy}")
            
            try:
                start_time = datetime.now()
                result = await orchestrator.get_optimal_comps(property_data, strategy)
                end_time = datetime.now()
                
                processing_time = (end_time - start_time).total_seconds()
                
                if result.get('success'):
                    comps_count = len(result.get('comps', []))
                    sources_used = result.get('sources_used', [])
                    
                    print(f"   ✅ Success: {comps_count} comps found")
                    print(f"   ⏱️  Time: {processing_time:.2f}s")
                    print(f"   📊 Sources: {', '.join(sources_used)}")
                    
                    # Show first comp as example
                    if result.get('comps'):
                        first_comp = result['comps'][0]
                        print(f"   🏠 Example comp: {first_comp.get('address', 'N/A')} - ${first_comp.get('sale_price', 'N/A')}")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   💥 Error: {str(e)}")
        
        print("-" * 40)
    
    # Show performance stats
    stats = orchestrator.get_performance_stats()
    print(f"\n📈 Performance Statistics:")
    print(f"   API Calls: {stats['stats']['api_calls']}")
    print(f"   Scraping Calls: {stats['stats']['scraping_calls']}")
    print(f"   Cache Hits: {stats['stats']['cache_hits']}")
    print(f"   Total Cost: ${stats['stats']['total_cost']:.2f}")
    print(f"   Cost per Property: ${stats['cost_per_property']:.2f}")

async def test_enhanced_workflow():
    """Test the enhanced comping workflow integration"""
    
    print("\n🔧 Testing Enhanced Workflow Integration")
    print("=" * 50)
    
    for i, property_data in enumerate(TEST_PROPERTIES):
        print(f"\n📍 Property {i+1}: {property_data['address']}")
        
        try:
            start_time = datetime.now()
            result = await run_enhanced_comping_workflow(property_data, "cost_optimized")
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comps_count = len(result.get('comps', []))
                print(f"   ✅ Success: {comps_count} comps found in {processing_time:.2f}s")
                
                # Show performance metrics if available
                if 'performance' in result:
                    perf = result['performance']
                    print(f"   📊 Strategy: {perf.get('strategy_used', 'N/A')}")
                    print(f"   💰 API Calls: {perf['stats']['api_calls']}")
                    print(f"   🕷️  Scraping Calls: {perf['stats']['scraping_calls']}")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")

async def test_batch_processing():
    """Test batch processing capabilities"""
    
    print("\n📦 Testing Batch Processing")
    print("=" * 50)
    
    orchestrator = SmartCompingOrchestrator()
    
    try:
        start_time = datetime.now()
        results = await orchestrator.batch_process_properties(TEST_PROPERTIES, "cost_optimized")
        end_time = datetime.now()
        
        total_time = (end_time - start_time).total_seconds()
        
        print(f"   ⏱️  Total batch time: {total_time:.2f}s")
        print(f"   📊 Properties processed: {len(results)}")
        print(f"   ⚡ Average time per property: {total_time/len(results):.2f}s")
        
        successful_results = [r for r in results if r['comps_result'].get('success')]
        print(f"   ✅ Successful: {len(successful_results)}/{len(results)}")
        
        total_comps = sum(len(r['comps_result'].get('comps', [])) for r in successful_results)
        print(f"   🏠 Total comps found: {total_comps}")
        
    except Exception as e:
        print(f"   💥 Batch processing error: {str(e)}")

async def test_api_availability():
    """Test which API sources are available"""
    
    print("\n🔌 Testing API Source Availability")
    print("=" * 50)
    
    api_keys = {
        "RealtyMole": os.getenv('REALTY_MOLE_API_KEY'),
        "SmartyStreets": os.getenv('SMARTY_STREETS_AUTH_ID') and os.getenv('SMARTY_STREETS_AUTH_TOKEN'),
        "RentSpree": os.getenv('RENTSPREE_API_KEY'),
        "ATTOM Data": os.getenv('ATTOM_DATA_API_KEY'),
        "PropertyRadar": os.getenv('PROPERTY_RADAR_API_KEY')
    }
    
    for api_name, has_key in api_keys.items():
        status = "✅ Available" if has_key else "❌ Missing API Key"
        print(f"   {api_name}: {status}")
    
    print(f"\n💡 Recommendation:")
    available_apis = [name for name, has_key in api_keys.items() if has_key]
    
    if available_apis:
        print(f"   Use API-first strategy with: {', '.join(available_apis)}")
    else:
        print(f"   Set up API keys for faster, more reliable comping")
        print(f"   Currently will fall back to scraping (slower but functional)")

def print_setup_instructions():
    """Print setup instructions for API sources"""
    
    print("\n📋 Setup Instructions for API Sources")
    print("=" * 50)
    
    instructions = {
        "RealtyMole": {
            "url": "https://realtymole.com/api",
            "cost": "$29/month for 1000 requests",
            "env_var": "REALTY_MOLE_API_KEY",
            "benefit": "Comprehensive property data and comparables"
        },
        "SmartyStreets": {
            "url": "https://www.smartystreets.com/",
            "cost": "$0.30 per lookup",
            "env_var": "SMARTY_STREETS_AUTH_ID and SMARTY_STREETS_AUTH_TOKEN",
            "benefit": "Address validation and property details"
        }
    }
    
    for api_name, info in instructions.items():
        print(f"\n🔧 {api_name}:")
        print(f"   URL: {info['url']}")
        print(f"   Cost: {info['cost']}")
        print(f"   Env Var: {info['env_var']}")
        print(f"   Benefit: {info['benefit']}")

async def main():
    """Main test function"""
    
    print("🚀 Enhanced Comping System Test Suite")
    print("=" * 60)
    
    # Test API availability first
    await test_api_availability()
    
    # Test smart orchestrator
    await test_smart_orchestrator()
    
    # Test enhanced workflow
    await test_enhanced_workflow()
    
    # Test batch processing
    await test_batch_processing()
    
    # Print setup instructions
    print_setup_instructions()
    
    print("\n✨ Test suite completed!")
    print("\n💡 Next Steps:")
    print("   1. Set up API keys for faster comping")
    print("   2. Configure scraper credentials for fallback")
    print("   3. Integrate with your lead processing workflow")
    print("   4. Monitor performance and adjust strategies")

if __name__ == "__main__":
    asyncio.run(main())
