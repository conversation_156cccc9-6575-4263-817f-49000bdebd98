#!/usr/bin/env python3
"""
Test script for RentCast API integration
Verifies that RentCast agent is working correctly with your API key
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.rentcast_agent import RentCastAgent, get_rentcast_comps, get_rentcast_value_estimate, get_rentcast_rent_estimate

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test properties
TEST_PROPERTIES = [
    {
        "address": "123 Main St",
        "city": "Detroit",
        "state": "MI",
        "zip_code": "48201",
        "beds": 3,
        "baths": 1,
        "sqft": 1200
    },
    {
        "address": "456 Oak Ave",
        "city": "Birmingham",
        "state": "MI", 
        "zip_code": "48009",
        "beds": 4,
        "baths": 3,
        "sqft": 2500
    }
]

async def test_rentcast_api_key():
    """Test if RentCast API key is configured"""
    print("🔑 Testing RentCast API Key Configuration")
    print("=" * 50)
    
    api_key = os.getenv('RENTCAST_API_KEY')
    
    if api_key:
        print(f"   ✅ RentCast API key found: {api_key[:10]}...")
        return True
    else:
        print(f"   ❌ RentCast API key not found")
        print(f"   💡 Set RENTCAST_API_KEY in your .env file")
        print(f"   📋 Get your key at: https://app.rentcast.io/app/api")
        return False

async def test_rentcast_agent_initialization():
    """Test RentCast agent initialization"""
    print("\n🤖 Testing RentCast Agent Initialization")
    print("=" * 50)
    
    try:
        async with RentCastAgent() as agent:
            print("   ✅ RentCast agent initialized successfully")
            print(f"   🔧 Base URL: {agent.config.base_url}")
            print(f"   ⏱️  Timeout: {agent.config.timeout}s")
            print(f"   🔄 Max retries: {agent.config.max_retries}")
            return True
    except ValueError as e:
        print(f"   ❌ Failed to initialize RentCast agent: {str(e)}")
        return False
    except Exception as e:
        print(f"   💥 Unexpected error: {str(e)}")
        return False

async def test_property_comps():
    """Test getting property comparables"""
    print("\n🏠 Testing Property Comparables")
    print("=" * 50)
    
    for i, property_data in enumerate(TEST_PROPERTIES):
        print(f"\n📍 Property {i+1}: {property_data['address']}, {property_data['city']}, {property_data['state']}")
        
        try:
            start_time = datetime.now()
            result = await get_rentcast_comps(property_data, radius_miles=1.0, limit=5)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comps = result.get('comps', [])
                print(f"   ✅ Success: {len(comps)} comps found in {processing_time:.2f}s")
                
                if comps:
                    # Show first comp as example
                    first_comp = comps[0]
                    print(f"   🏠 Example comp: {first_comp.get('address', 'N/A')}")
                    print(f"   💰 Sale price: ${first_comp.get('sale_price', 'N/A'):,}" if first_comp.get('sale_price') else "   💰 Sale price: N/A")
                    print(f"   📅 Sale date: {first_comp.get('sale_date', 'N/A')}")
                    print(f"   🛏️  Beds/Baths: {first_comp.get('beds', 'N/A')}/{first_comp.get('baths', 'N/A')}")
                    print(f"   📐 Sqft: {first_comp.get('sqft', 'N/A'):,}" if first_comp.get('sqft') else "   📐 Sqft: N/A")
                else:
                    print("   ⚠️  No comps returned (may be valid for some areas)")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")

async def test_value_estimates():
    """Test getting property value estimates"""
    print("\n💰 Testing Property Value Estimates")
    print("=" * 50)
    
    for i, property_data in enumerate(TEST_PROPERTIES):
        print(f"\n📍 Property {i+1}: {property_data['address']}, {property_data['city']}, {property_data['state']}")
        
        try:
            start_time = datetime.now()
            result = await get_rentcast_value_estimate(property_data)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                value_estimate = result.get('value_estimate')
                confidence = result.get('confidence_score')
                value_range = result.get('value_range', {})
                
                print(f"   ✅ Success: Value estimate in {processing_time:.2f}s")
                print(f"   💰 Estimated value: ${value_estimate:,}" if value_estimate else "   💰 Estimated value: N/A")
                print(f"   📊 Confidence: {confidence:.1%}" if confidence else "   📊 Confidence: N/A")
                
                if value_range.get('low') and value_range.get('high'):
                    print(f"   📈 Range: ${value_range['low']:,} - ${value_range['high']:,}")
                
                comps_used = result.get('comparables_used', 0)
                print(f"   🏠 Comparables used: {comps_used}")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")

async def test_rent_estimates():
    """Test getting rental estimates"""
    print("\n🏠 Testing Rental Estimates")
    print("=" * 50)
    
    for i, property_data in enumerate(TEST_PROPERTIES):
        print(f"\n📍 Property {i+1}: {property_data['address']}, {property_data['city']}, {property_data['state']}")
        
        try:
            start_time = datetime.now()
            result = await get_rentcast_rent_estimate(property_data)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                rent_estimate = result.get('rent_estimate')
                confidence = result.get('confidence_score')
                rent_range = result.get('rent_range', {})
                
                print(f"   ✅ Success: Rent estimate in {processing_time:.2f}s")
                print(f"   💰 Estimated rent: ${rent_estimate:,}/month" if rent_estimate else "   💰 Estimated rent: N/A")
                print(f"   📊 Confidence: {confidence:.1%}" if confidence else "   📊 Confidence: N/A")
                
                if rent_range.get('low') and rent_range.get('high'):
                    print(f"   📈 Range: ${rent_range['low']:,} - ${rent_range['high']:,}/month")
                
                comps_used = result.get('comparables_used', 0)
                print(f"   🏠 Comparables used: {comps_used}")
            else:
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")

async def test_enhanced_comping_integration():
    """Test integration with enhanced comping system"""
    print("\n🔧 Testing Enhanced Comping Integration")
    print("=" * 50)
    
    try:
        from agents.workflows.smart_comping_orchestrator import get_smart_comps
        
        property_data = TEST_PROPERTIES[0]
        print(f"📍 Testing with: {property_data['address']}, {property_data['city']}, {property_data['state']}")
        
        start_time = datetime.now()
        result = await get_smart_comps(property_data, "cost_optimized")
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        if result.get('success'):
            comps = result.get('comps', [])
            sources_used = result.get('sources_used', [])
            
            print(f"   ✅ Success: {len(comps)} comps found in {processing_time:.2f}s")
            print(f"   📊 Sources used: {', '.join(sources_used)}")
            
            if 'performance' in result:
                perf = result['performance']
                print(f"   🎯 Strategy: {perf.get('strategy_used', 'N/A')}")
                print(f"   💰 API calls: {perf['stats']['api_calls']}")
                print(f"   🕷️  Scraping calls: {perf['stats']['scraping_calls']}")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
            
    except ImportError as e:
        print(f"   ⚠️  Enhanced comping system not available: {str(e)}")
    except Exception as e:
        print(f"   💥 Error: {str(e)}")

def print_setup_guide():
    """Print setup guide for RentCast"""
    print("\n📋 RentCast Setup Guide")
    print("=" * 50)
    
    print("\n1. 🔑 Get your RentCast API key:")
    print("   • Visit: https://app.rentcast.io/app/api")
    print("   • Sign up for a free account (50 requests/month)")
    print("   • Create an API key")
    
    print("\n2. 🔧 Configure your environment:")
    print("   • Add to your .env file: RENTCAST_API_KEY=your_key_here")
    print("   • Restart your application")
    
    print("\n3. 💰 Pricing options:")
    print("   • Developer (Free): 50 requests/month")
    print("   • Starter: $39/month for 500 requests")
    print("   • Professional: $99/month for 2000 requests")
    print("   • Business: $299/month for 10000 requests")
    
    print("\n4. 🚀 Integration benefits:")
    print("   • Replace slow scraping with fast API calls")
    print("   • Get comprehensive property data and comps")
    print("   • Automated valuation models (AVM)")
    print("   • Rental estimates for investment analysis")

async def main():
    """Main test function"""
    
    print("🧪 RentCast Integration Test Suite")
    print("=" * 60)
    
    # Test API key configuration
    has_api_key = await test_rentcast_api_key()
    
    if not has_api_key:
        print_setup_guide()
        return
    
    # Test agent initialization
    agent_works = await test_rentcast_agent_initialization()
    
    if not agent_works:
        return
    
    # Test core functionality
    await test_property_comps()
    await test_value_estimates()
    await test_rent_estimates()
    
    # Test integration
    await test_enhanced_comping_integration()
    
    print("\n✨ RentCast test suite completed!")
    print("\n💡 Next Steps:")
    print("   1. If tests passed, RentCast is ready to use")
    print("   2. Run the enhanced comping test: python scripts/test_enhanced_comping.py")
    print("   3. Integrate with your lead processing workflow")
    print("   4. Monitor API usage on your RentCast dashboard")

if __name__ == "__main__":
    asyncio.run(main())
