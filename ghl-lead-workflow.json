{"name": "GHL Lead → Supabase Production", "nodes": [{"parameters": {"path": "ghl/lead", "options": {"noResponseBody": false}, "responseMode": "responseNode"}, "id": "webhook-trigger", "name": "GHL Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "ghl-lead-webhook-production"}, {"parameters": {"jsCode": "// Extract and validate GHL contact data\nconst inputData = $input.all();\nconst webhookData = inputData[0].json;\n\n// Log incoming data for debugging\nconsole.log('Received GHL webhook data:', JSON.stringify(webhookData, null, 2));\n\n// Extract contact information with fallbacks\nconst contact = webhookData.contact || webhookData;\nconst customFields = contact.customFields || [];\nconst tags = contact.tags || [];\n\n// Helper function to get custom field value\nfunction getCustomFieldValue(fields, fieldName) {\n  const field = fields.find(f => f.name === fieldName || f.key === fieldName);\n  return field ? field.value : null;\n}\n\n// Enhanced property data extraction\nconst propertyValue = getCustomFieldValue(customFields, 'property_value') || \n                    getCustomFieldValue(customFields, 'estimated_value') || \n                    getCustomFieldValue(customFields, 'asking_price') || 0;\n\nconst equity = getCustomFieldValue(customFields, 'equity') || \n              getCustomFieldValue(customFields, 'equity_amount') || 0;\n\nconst motivation = getCustomFieldValue(customFields, 'motivation') || \n                  getCustomFieldValue(customFields, 'seller_motivation') || \n                  getCustomFieldValue(customFields, 'reason_for_selling') || '';\n\nconst timeline = getCustomFieldValue(customFields, 'timeline') || \n                getCustomFieldValue(customFields, 'when_to_sell') || \n                getCustomFieldValue(customFields, 'closing_timeline') || '';\n\nconst condition = getCustomFieldValue(customFields, 'condition') || \n                 getCustomFieldValue(customFields, 'property_condition') || \n                 getCustomFieldValue(customFields, 'repair_needed') || '';\n\n// Map GHL data to enhanced Supabase leads schema\nconst leadData = {\n  // Basic contact information\n  first_name: contact.firstName || contact.first_name || '',\n  last_name: contact.lastName || contact.last_name || '',\n  email: contact.email || '',\n  phone: contact.phone || contact.phoneNumber || '',\n  \n  // Address information\n  address_line_1: contact.address1 || contact.address || '',\n  address_line_2: contact.address2 || '',\n  city: contact.city || '',\n  state: contact.state || '',\n  postal_code: contact.postalCode || contact.zipCode || '',\n  country: contact.country || 'US',\n  \n  // Property information\n  property_address: `${contact.address1 || ''} ${contact.city || ''} ${contact.state || ''}`.trim(),\n  property_value: propertyValue,\n  equity: equity,\n  condition: condition,\n  \n  // Seller information\n  motivation: motivation,\n  timeline: timeline,\n  situation: getCustomFieldValue(customFields, 'situation') || '',\n  \n  // Lead classification and source\n  lead_source: 'gohighlevel',\n  lead_status: 'new',\n  lead_type: getCustomFieldValue(customFields, 'lead_type') || 'seller',\n  priority: 'medium', // Will be updated by scoring\n  tier: 2, // Will be updated by scoring\n  \n  // Investment and financial information\n  investment_experience: getCustomFieldValue(customFields, 'investment_experience') || 'beginner',\n  investment_goals: getCustomFieldValue(customFields, 'investment_goals') || '',\n  risk_tolerance: getCustomFieldValue(customFields, 'risk_tolerance') || 'moderate',\n  annual_income: getCustomFieldValue(customFields, 'annual_income') || null,\n  net_worth: getCustomFieldValue(customFields, 'net_worth') || null,\n  liquid_capital: getCustomFieldValue(customFields, 'liquid_capital') || null,\n  \n  // Communication preferences\n  preferred_contact_method: getCustomFieldValue(customFields, 'preferred_contact_method') || 'email',\n  communication_frequency: getCustomFieldValue(customFields, 'communication_frequency') || 'weekly',\n  \n  // Metadata\n  ghl_contact_id: contact.id || contact.contactId || '',\n  tags: tags.join(', '),\n  notes: contact.notes || '',\n  comments: getCustomFieldValue(customFields, 'comments') || '',\n  \n  // Timestamps\n  created_at: new Date().toISOString(),\n  updated_at: new Date().toISOString()\n};\n\n// Validate required fields\nif (!leadData.email && !leadData.phone) {\n  throw new Error('Either email or phone is required for lead creation');\n}\n\n// Log processed data\nconsole.log('Processed lead data:', JSON.stringify(leadData, null, 2));\n\nreturn [{ json: leadData }];"}, "id": "data-processor", "name": "Process GHL Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "insert", "schema": {"__rl": true, "value": "public", "mode": "list"}, "table": {"__rl": true, "value": "leads", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"first_name": "={{ $json.first_name }}", "last_name": "={{ $json.last_name }}", "email": "={{ $json.email }}", "phone": "={{ $json.phone }}", "address_line_1": "={{ $json.address_line_1 }}", "address_line_2": "={{ $json.address_line_2 }}", "city": "={{ $json.city }}", "state": "={{ $json.state }}", "postal_code": "={{ $json.postal_code }}", "country": "={{ $json.country }}", "lead_source": "={{ $json.lead_source }}", "lead_status": "={{ $json.lead_status }}", "lead_type": "={{ $json.lead_type }}", "investment_experience": "={{ $json.investment_experience }}", "investment_goals": "={{ $json.investment_goals }}", "risk_tolerance": "={{ $json.risk_tolerance }}", "annual_income": "={{ $json.annual_income }}", "net_worth": "={{ $json.net_worth }}", "liquid_capital": "={{ $json.liquid_capital }}", "preferred_contact_method": "={{ $json.preferred_contact_method }}", "communication_frequency": "={{ $json.communication_frequency }}", "ghl_contact_id": "={{ $json.ghl_contact_id }}", "tags": "={{ $json.tags }}", "notes": "={{ $json.notes }}", "created_at": "={{ $json.created_at }}", "updated_at": "={{ $json.updated_at }}"}, "matchingColumns": [], "schema": [{"id": "first_name", "displayName": "first_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "last_name", "displayName": "last_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}]}, "options": {}}, "id": "supabase-insert", "name": "Insert Lead to Supabase", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 300], "credentials": {"postgres": {"id": "supabase-connection", "name": "Supabase Connection"}}}, {"parameters": {"method": "POST", "url": "http://api:5002/api/v1/leads/process-enhanced", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "lead_id", "value": "={{ $json.id }}"}, {"name": "lead_data", "value": "={{ JSON.stringify($json) }}"}]}, "options": {"timeout": 30000}}, "id": "enhanced-processing", "name": "Enhanced Lead Processing", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"jsCode": "// Process enhanced lead processing results\nconst leadData = $input.first().json;\nconst processingResult = leadData.body ? JSON.parse(leadData.body) : leadData;\n\nconsole.log('Enhanced processing result:', JSON.stringify(processingResult, null, 2));\n\n// Extract key information\nconst leadScore = processingResult.lead_score || {};\nconst qualification = processingResult.qualification || {};\nconst followUpSequence = processingResult.follow_up_sequence || [];\nconst isHotLead = processingResult.is_hot_lead || false;\nconst agentCommunication = processingResult.agent_communication || {};\n\n// Log important metrics\nconsole.log(`Lead Score: ${leadScore.total_score || 'N/A'} (Tier ${leadScore.tier || 'N/A'}, Priority: ${leadScore.priority || 'N/A'})`);\nconsole.log(`Qualified: ${qualification.qualified ? 'YES' : 'NO'} (Score: ${qualification.score || 'N/A'})`);\nconsole.log(`Hot Lead: ${isHotLead ? 'YES' : 'NO'}`);\nconsole.log(`Follow-up actions: ${followUpSequence.length || 0}`);\n\n// Determine next workflow path based on tier and hot lead status\nlet nextPath = 'standard';\nif (leadScore.tier === 3) {\n  nextPath = 'tier3_dnc';\n  console.log('Lead classified as Tier 3 (DNC) - no further processing');\n} else if (qualification.qualified && leadScore.tier === 1) {\n  if (isHotLead) {\n    nextPath = 'tier1_hot_lead';\n    console.log('Hot Tier 1 lead detected - immediate processing path');\n  } else {\n    nextPath = 'tier1_qualified';\n    console.log('Standard Tier 1 lead - normal processing path');\n  }\n} else if (qualification.qualified && leadScore.tier === 2) {\n  nextPath = 'tier2_qualified';\n  console.log('Tier 2 lead - follow-up sequence');\n} else {\n  nextPath = 'nurture';\n  console.log('Lead not qualified - nurture sequence');\n}\n\nreturn [{\n  json: {\n    success: true,\n    message: 'Lead successfully processed with enhanced automation',\n    leadId: processingResult.lead_id,\n    score: leadScore.total_score,\n    tier: leadScore.tier,\n    priority: leadScore.priority,\n    qualified: qualification.qualified,\n    isHotLead: isHotLead,\n    nextPath: nextPath,\n    followUpActions: followUpSequence.length,\n    agentCommunication: agentCommunication,\n    recommendations: leadScore.recommendations || [],\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "process-results", "name": "Process Enhanced Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "tier1_qualified", "leftValue": "={{ $json.nextPath }}", "rightValue": "tier1_qualified", "operator": {"type": "string", "operation": "equals"}}, {"id": "tier1_hot_lead", "leftValue": "={{ $json.nextPath }}", "rightValue": "tier1_hot_lead", "operator": {"type": "string", "operation": "equals"}}, {"id": "tier2_qualified", "leftValue": "={{ $json.nextPath }}", "rightValue": "tier2_qualified", "operator": {"type": "string", "operation": "equals"}}, {"id": "tier3_dnc", "leftValue": "={{ $json.nextPath }}", "rightValue": "tier3_dnc", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "routing-switch", "name": "Lead Routing <PERSON><PERSON>", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1340, 300]}, {"parameters": {"method": "POST", "url": "http://api:5002/api/v1/workflows/comping", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "lead_id", "value": "={{ $json.leadId }}"}]}}, "id": "trigger-comping", "name": "Trigger Comping Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 200], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"method": "POST", "url": "http://api:5002/api/v1/workflows/followup", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "lead_id", "value": "={{ $json.leadId }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "tier", "value": "={{ $json.tier }}"}]}}, "id": "trigger-followup", "name": "Trigger Follow-up Sequence", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 400], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"method": "POST", "url": "http://api:5002/api/v1/workflows/hot-lead-processing", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "lead_id", "value": "={{ $json.leadId }}"}, {"name": "priority", "value": "critical"}, {"name": "tier", "value": "={{ $json.tier }}"}, {"name": "is_hot_lead", "value": "={{ $json.isHotLead }}"}]}}, "id": "trigger-hot-lead-processing", "name": "Trigger Hot Lead Processing", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 100], "credentials": {"httpHeaderAuth": {"id": "api-auth", "name": "API Authentication"}}}, {"parameters": {"jsCode": "// Handle Tier 3 (DNC) leads\nconst leadData = $input.first().json;\n\nconsole.log('Processing Tier 3 (DNC) lead:', leadData.leadId);\n\n// Log the DNC classification\nconst dncResult = {\n  success: true,\n  message: 'Lead classified as Tier 3 (Do Not Contact)',\n  leadId: leadData.leadId,\n  tier: 3,\n  action: 'Added to DNC list',\n  timestamp: new Date().toISOString()\n};\n\n// In a real implementation, you would:\n// 1. Add to DNC list in CRM\n// 2. Stop all automated communications\n// 3. Log the DNC status\n// 4. Notify team if needed\n\nconsole.log('DNC processing complete:', JSON.stringify(dncResult, null, 2));\n\nreturn [{ json: dncResult }];"}, "id": "handle-tier3-dnc", "name": "Handle Tier 3 (DNC)", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 500]}, {"parameters": {"jsCode": "// Log successful workflow completion\nconst results = $input.all();\nconsole.log('Workflow completed successfully:', JSON.stringify(results, null, 2));\n\n// Compile final response\nconst finalResult = {\n  success: true,\n  message: 'Lead processing workflow completed successfully',\n  workflows_triggered: results.map(r => r.json?.workflow || 'unknown'),\n  timestamp: new Date().toISOString()\n};\n\nreturn [{ json: finalResult }];"}, "id": "final-success", "name": "Final Success Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"jsCode": "// Handle errors and log them\nconst error = $input.first().json;\nconsole.error('Error processing GHL webhook:', error);\n\n// Return error response\nreturn [{\n  json: {\n    success: false,\n    error: error.message || 'Unknown error occurred',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 480]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 500}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 480]}], "connections": {"GHL Webhook Trigger": {"main": [[{"node": "Process GHL Data", "type": "main", "index": 0}]]}, "Process GHL Data": {"main": [[{"node": "Insert Lead to Supabase", "type": "main", "index": 0}]]}, "Insert Lead to Supabase": {"main": [[{"node": "Enhanced Lead Processing", "type": "main", "index": 0}]]}, "Enhanced Lead Processing": {"main": [[{"node": "Process Enhanced Results", "type": "main", "index": 0}]]}, "Process Enhanced Results": {"main": [[{"node": "Lead Routing <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Lead Routing Switch": {"main": [[{"node": "Trigger Comping Workflow", "type": "main", "index": 0}], [{"node": "Trigger Hot Lead Processing", "type": "main", "index": 0}], [{"node": "Trigger Follow-up Sequence", "type": "main", "index": 0}], [{"node": "Handle Tier 3 (DNC)", "type": "main", "index": 0}]]}, "Trigger Comping Workflow": {"main": [[{"node": "Final Success Response", "type": "main", "index": 0}]]}, "Trigger Follow-up Sequence": {"main": [[{"node": "Final Success Response", "type": "main", "index": 0}]]}, "Trigger Hot Lead Processing": {"main": [[{"node": "Final Success Response", "type": "main", "index": 0}]]}, "Handle Tier 3 (DNC)": {"main": [[{"node": "Final Success Response", "type": "main", "index": 0}]]}, "Final Success Response": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-06-01T07:45:17.000Z", "updatedAt": "2025-06-01T07:45:17.000Z", "id": "ghl-integration", "name": "GHL Integration"}], "triggerCount": 1, "updatedAt": "2025-06-01T07:45:17.000Z", "versionId": "1", "active": true}