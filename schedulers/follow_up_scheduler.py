
import os
import time
import random
from datetime import datetime, timed<PERSON><PERSON>
from zoneinfo import ZoneInfo
import logging
import json

from supabase import create_client
from agents.followup_bot import FollowUpBot
from agents.workflows.tier_classifier import classify_lead_tier
from agents.workflows.agent_communication_workflow import execute_agent_communication
from agents.dispo_bot import DispoBot
from logs.agent_run_logs import log_agent_run
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Supabase client
supabase = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_KEY"))

# Communication window in EST
EST_ZONE = ZoneInfo("America/New_York")
START_HOUR = 9
END_HOUR = 18

# Schedule configuration
FOLLOW_UP_STAGES = {
    "initial": {"days": 2, "priority": "high"},
    "reminder": {"days": 5, "priority": "medium"},
    "final": {"days": 10, "priority": "low"}
}

DISPO_STAGES = {
    "initial_offer": {"days": 0, "priority": "high"},
    "first_follow_up": {"days": 3, "priority": "medium"},
    "second_follow_up": {"days": 7, "priority": "medium"},
    "final_follow_up": {"days": 14, "priority": "low"}
}

def is_in_dnc_list(lead_data) -> bool:
    """
    Check if a lead is in the Do Not Contact (DNC) list.

    Args:
        lead_data (dict): Lead data from Supabase

    Returns:
        bool: True if the lead is in the DNC list, False otherwise
    """
    # Check for explicit DNC flag
    if lead_data.get("dnc", False):
        return True

    # Check for opt-out in notes, comments, or status
    notes = lead_data.get("notes", "").lower()
    comments = lead_data.get("comments", "").lower()
    status = lead_data.get("status", "").lower()

    # Check for opt-out indicators
    dnc_indicators = [
        "do not contact", "dnc", "opted out", "opt out",
        "remove from list", "stop texting", "stop messaging",
        "unsubscribe", "don't contact", "remove me"
    ]

    text_to_check = f"{notes} {comments} {status}"
    for indicator in dnc_indicators:
        if indicator in text_to_check:
            return True

    return False

def is_within_allowed_time_window() -> bool:
    """
    Check if the current time is within the allowed communication window (9am-6pm EST, weekdays).

    Returns:
        bool: True if within allowed window, False otherwise
    """
    # Get current time in EST
    now_est = datetime.now(tz=EST_ZONE)

    # Check if within 9am-6pm EST
    if START_HOUR <= now_est.hour < END_HOUR:
        # Check for weekends
        if now_est.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
            logger.info(f"Outside allowed contact time window (weekend): {now_est}")
            return False
        return True

    logger.info(f"Outside allowed contact time window: {now_est.hour}h EST")
    return False

def get_pending_leads(max_leads=10):
    """
    Get leads that are due for follow-up.

    Args:
        max_leads (int): Maximum number of leads to return

    Returns:
        list: List of lead IDs and metadata for leads due for follow-up
    """
    try:
        # Default cutoff (6 hours ago) for leads that haven't been followed up recently
        default_cutoff = (datetime.utcnow() - timedelta(hours=6)).isoformat()

        # Get current time in EST for schedule checks
        now_est = datetime.now(tz=EST_ZONE)

        # Query for tier 1 leads that are due for follow-up
        res = supabase.table("leads")\
            .select("id, first_name, last_name, email, phone, status, notes, comments, dnc, ghl_contact_id, last_followup, follow_up_count, follow_up_stage, created_at")\
            .eq("tier", "tier1")\
            .lte("last_followup", default_cutoff)\
            .limit(max_leads)\
            .execute()

        pending_leads = []
        for lead in (res.data or []):
            # Skip leads in DNC list
            if is_in_dnc_list(lead):
                logger.info(f"Skipping lead {lead['id']} - in DNC list")
                continue

            # Determine follow-up stage and next follow-up date
            follow_up_stage = lead.get("follow_up_stage", "initial")
            follow_up_count = lead.get("follow_up_count", 0)

            # Get created_at timestamp
            created_at = lead.get("created_at")
            if created_at:
                try:
                    created_at = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                except (ValueError, TypeError):
                    created_at = None

            # Determine the expected follow-up date based on stage
            if follow_up_stage in FOLLOW_UP_STAGES:
                days_to_wait = FOLLOW_UP_STAGES[follow_up_stage]["days"]
            else:
                # Default to initial stage if unknown
                days_to_wait = FOLLOW_UP_STAGES["initial"]["days"]

            # Calculate the expected follow-up date
            expected_follow_up_date = None
            if created_at:
                expected_follow_up_date = created_at + timedelta(days=days_to_wait)

            # If we're past the expected follow-up date, add to pending leads
            if not expected_follow_up_date or datetime.utcnow() >= expected_follow_up_date:
                pending_leads.append({
                    "id": lead["id"],
                    "follow_up_stage": follow_up_stage,
                    "follow_up_count": follow_up_count,
                    "ghl_contact_id": lead.get("ghl_contact_id"),
                    "first_name": lead.get("first_name", ""),
                    "last_name": lead.get("last_name", ""),
                    "email": lead.get("email", ""),
                    "phone": lead.get("phone", ""),
                    "property_address": lead.get("property_address", "")
                })

        logger.info(f"Found {len(pending_leads)} pending leads for follow-up")
        return pending_leads

    except Exception as e:
        logger.error(f"Error getting pending leads: {str(e)}")
        return []

def get_pending_dispositions(max_properties=10):
    """
    Get properties that are due for disposition actions.

    Args:
        max_properties (int): Maximum number of properties to return

    Returns:
        list: List of property IDs and metadata for properties due for disposition
    """
    try:
        # Default cutoff (1 day ago) for properties that haven't had disposition actions recently
        default_cutoff = (datetime.utcnow() - timedelta(days=1)).isoformat()

        # Query for properties that need disposition actions
        res = supabase.table("properties")\
            .select("id, lead_id, address, status, last_disposition_action, disposition_stage, created_at")\
            .in_("status", ["active", "pending", "analyzing"])\
            .lte("last_disposition_action", default_cutoff)\
            .limit(max_properties)\
            .execute()

        pending_dispositions = []
        for property_data in (res.data or []):
            # If we have a lead ID, check if it's in DNC list
            lead_id = property_data.get("lead_id")
            if lead_id:
                # Get lead data
                lead_res = supabase.table("leads")\
                    .select("id, dnc, notes, comments, status")\
                    .eq("id", lead_id)\
                    .execute()

                if lead_res.data and len(lead_res.data) > 0:
                    lead_data = lead_res.data[0]
                    if is_in_dnc_list(lead_data):
                        logger.info(f"Skipping property {property_data['id']} - lead in DNC list")
                        continue

            # Determine disposition stage and next action date
            disposition_stage = property_data.get("disposition_stage", "initial_offer")

            # Get created_at timestamp
            created_at = property_data.get("created_at")
            if created_at:
                try:
                    created_at = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                except (ValueError, TypeError):
                    created_at = None

            # Determine the expected disposition date based on stage
            if disposition_stage in DISPO_STAGES:
                days_to_wait = DISPO_STAGES[disposition_stage]["days"]
            else:
                # Default to initial stage if unknown
                days_to_wait = DISPO_STAGES["initial_offer"]["days"]

            # Calculate the expected disposition date
            expected_disposition_date = None
            if created_at:
                expected_disposition_date = created_at + timedelta(days=days_to_wait)

            # If we're past the expected disposition date, add to pending dispositions
            if not expected_disposition_date or datetime.utcnow() >= expected_disposition_date:
                pending_dispositions.append({
                    "id": property_data["id"],
                    "lead_id": lead_id,
                    "disposition_stage": disposition_stage
                })

        logger.info(f"Found {len(pending_dispositions)} pending properties for disposition")
        return pending_dispositions

    except Exception as e:
        logger.error(f"Error getting pending dispositions: {str(e)}")
        return []

def update_follow_up_stage(lead_id, current_stage):
    """
    Update the follow-up stage for a lead.

    Args:
        lead_id (str): Lead ID
        current_stage (str): Current follow-up stage

    Returns:
        str: New follow-up stage
    """
    # Determine next stage based on current stage
    if current_stage == "initial":
        new_stage = "reminder"
    elif current_stage == "reminder":
        new_stage = "final"
    else:
        # If already at final stage, stay there
        new_stage = "final"

    try:
        # Update the lead record with new stage
        supabase.table("leads")\
            .update({
                "follow_up_stage": new_stage,
                "last_followup": datetime.utcnow().isoformat()
            })\
            .eq("id", lead_id)\
            .execute()

        logger.info(f"Updated follow-up stage for lead {lead_id}: {current_stage} -> {new_stage}")
        return new_stage
    except Exception as e:
        logger.error(f"Error updating follow-up stage: {str(e)}")
        return current_stage

def update_disposition_stage(property_id, current_stage):
    """
    Update the disposition stage for a property.

    Args:
        property_id (str): Property ID
        current_stage (str): Current disposition stage

    Returns:
        str: New disposition stage
    """
    # Determine next stage based on current stage
    if current_stage == "initial_offer":
        new_stage = "first_follow_up"
    elif current_stage == "first_follow_up":
        new_stage = "second_follow_up"
    elif current_stage == "second_follow_up":
        new_stage = "final_follow_up"
    else:
        # If already at final stage, stay there
        new_stage = "final_follow_up"

    try:
        # Update the property record with new stage
        supabase.table("properties")\
            .update({
                "disposition_stage": new_stage,
                "last_disposition_action": datetime.utcnow().isoformat()
            })\
            .eq("id", property_id)\
            .execute()

        logger.info(f"Updated disposition stage for property {property_id}: {current_stage} -> {new_stage}")
        return new_stage
    except Exception as e:
        logger.error(f"Error updating disposition stage: {str(e)}")
        return current_stage


def run_follow_up_bot(lead_id, follow_up_stage, follow_up_count, lead_data=None):
    """
    Run the follow-up bot for a lead.

    Args:
        lead_id (str): Lead ID
        follow_up_stage (str): Current follow-up stage
        follow_up_count (int): Current follow-up count
        lead_data (dict, optional): Additional lead data for context

    Returns:
        bool: True if follow-up was successful, False otherwise
    """
    try:
        # Initialize and run the follow-up bot
        bot = FollowUpBot(agent_name="follow_up_bot", lead_id=lead_id, entity=lead_id)

        # Add additional context if provided
        if lead_data:
            bot.context.update({"lead": lead_data})

        # Run the bot
        result = bot.run()

        # Log the run
        log_agent_run("follow_up_bot", lead_id, lead_id, result)

        # Update follow-up count and timestamp
        try:
            supabase.table("leads")\
                .update({
                    "last_followup": datetime.utcnow().isoformat(),
                    "follow_up_count": follow_up_count + 1
                })\
                .eq("id", lead_id)\
                .execute()
        except Exception as e:
            logger.error(f"Error updating follow-up count: {str(e)}")

        # Update follow-up stage
        new_stage = update_follow_up_stage(lead_id, follow_up_stage)

        # If this was the final follow-up, add a note to the lead
        if new_stage == "final":
            try:
                # Get GHL contact ID
                ghl_contact_id = None
                if lead_data:
                    ghl_contact_id = lead_data.get("ghl_contact_id")

                if ghl_contact_id:
                    # Initialize GHL client
                    from agents.ghl_client import GHLClient
                    ghl_client = GHLClient()

                    # Add a note about final follow-up
                    note = "Final follow-up sent. No further automated follow-ups will be sent."
                    ghl_client.add_note(ghl_contact_id, note)
            except Exception as e:
                logger.error(f"Error adding final follow-up note: {str(e)}")

        return "error" not in result.lower()

    except Exception as e:
        logger.error(f"Error running follow-up bot: {str(e)}")
        log_agent_run("follow_up_bot", lead_id, lead_id, str(e), status="error")
        return False

def run_dispo_bot(property_id, lead_id, disposition_stage):
    """
    Run the disposition bot for a property.

    Args:
        property_id (str): Property ID
        lead_id (str): Lead ID
        disposition_stage (str): Current disposition stage

    Returns:
        bool: True if disposition action was successful, False otherwise
    """
    try:
        # Initialize and run the disposition bot
        bot = DispoBot(
            agent_name="dispo_bot",
            lead_id=lead_id,
            entity=property_id,
            property_id=property_id
        )

        # Run the bot
        result = bot.run()

        # Log the run
        log_agent_run("dispo_bot", lead_id, property_id, result)

        # Update disposition timestamp
        try:
            supabase.table("properties")\
                .update({
                    "last_disposition_action": datetime.utcnow().isoformat()
                })\
                .eq("id", property_id)\
                .execute()
        except Exception as e:
            logger.error(f"Error updating disposition timestamp: {str(e)}")

        # Update disposition stage
        update_disposition_stage(property_id, disposition_stage)

        return "error" not in result.lower()

    except Exception as e:
        logger.error(f"Error running disposition bot: {str(e)}")
        log_agent_run("dispo_bot", lead_id, property_id, str(e), status="error")
        return False


def run_scheduler(max_leads=5, max_properties=5, dry_run=False):
    """
    Run the follow-up and disposition scheduler with improved timing logic.

    Args:
        max_leads (int): Maximum number of leads to process
        max_properties (int): Maximum number of properties to process
        dry_run (bool): If True, don't actually send messages or update records

    Returns:
        dict: Summary of scheduler run
    """
    logger.info("Starting follow-up and disposition scheduler")

    # Check if within allowed time window
    if not is_within_allowed_time_window():
        logger.info("Outside allowed contact window (9am-6pm EST, weekdays only), skipping scheduler run")
        return {
            "success": False,
            "reason": "outside_contact_window",
            "leads_processed": 0,
            "properties_processed": 0
        }

    # Get pending leads for follow-up
    pending_leads = get_pending_leads(max_leads)

    # Track results
    leads_processed = 0
    leads_successful = 0
    properties_processed = 0
    properties_successful = 0

    # Process follow-ups for pending leads
    for lead in pending_leads:
        lead_id = lead["id"]
        follow_up_stage = lead.get("follow_up_stage", "initial")
        follow_up_count = lead.get("follow_up_count", 0)

        # Random delay between 30 seconds and 3 minutes
        delay = random.randint(30, 180)
        logger.info(f"Delaying follow-up for {lead_id} by {delay}s")

        if not dry_run:
            time.sleep(delay)

            # Run follow-up bot
            success = run_follow_up_bot(lead_id, follow_up_stage, follow_up_count, lead)

            leads_processed += 1
            if success:
                leads_successful += 1
        else:
            logger.info(f"[DRY RUN] Would run follow-up bot for lead {lead_id}")
            leads_processed += 1

    # Get pending properties for disposition
    pending_dispositions = get_pending_dispositions(max_properties)

    # Process disposition actions for pending properties
    for property_data in pending_dispositions:
        property_id = property_data["id"]
        lead_id = property_data.get("lead_id")
        disposition_stage = property_data.get("disposition_stage", "initial_offer")

        if not lead_id:
            logger.warning(f"No lead ID found for property {property_id}, skipping disposition")
            continue

        # Random delay between 30 seconds and 3 minutes
        delay = random.randint(30, 180)
        logger.info(f"Delaying disposition for property {property_id} by {delay}s")

        if not dry_run:
            time.sleep(delay)

            # Run disposition bot
            success = run_dispo_bot(property_id, lead_id, disposition_stage)

            properties_processed += 1
            if success:
                properties_successful += 1
        else:
            logger.info(f"[DRY RUN] Would run disposition bot for property {property_id}")
            properties_processed += 1

    # Return summary
    return {
        "success": True,
        "timestamp": datetime.utcnow().isoformat(),
        "leads_processed": leads_processed,
        "leads_successful": leads_successful,
        "properties_processed": properties_processed,
        "properties_successful": properties_successful,
        "dry_run": dry_run
    }

def run_tier2_biweekly_scheduler():
    """
    Run the Tier 2 bi-weekly follow-up scheduler
    """
    try:
        logger.info("Starting Tier 2 bi-weekly follow-up scheduler")

        # Get all Tier 2 leads that need bi-weekly follow-up
        tier2_leads = get_tier2_leads_for_followup()

        if not tier2_leads:
            logger.info("No Tier 2 leads found for bi-weekly follow-up")
            return

        logger.info(f"Found {len(tier2_leads)} Tier 2 leads for bi-weekly follow-up")

        for lead in tier2_leads:
            try:
                lead_id = lead.get('id')
                logger.info(f"Processing Tier 2 follow-up for lead {lead_id}")

                # Check if it's time for follow-up (every 14 days)
                if should_send_tier2_followup(lead):
                    # Send bi-weekly check-in
                    result = send_tier2_biweekly_checkin(lead_id, lead)

                    if result.get('success'):
                        logger.info(f"Successfully sent Tier 2 follow-up for lead {lead_id}")
                        # Update last follow-up timestamp
                        update_tier2_followup_timestamp(lead_id)
                    else:
                        logger.error(f"Failed to send Tier 2 follow-up for lead {lead_id}: {result.get('error')}")
                else:
                    logger.debug(f"Not yet time for Tier 2 follow-up for lead {lead_id}")

            except Exception as e:
                logger.error(f"Error processing Tier 2 follow-up for lead {lead.get('id', 'unknown')}: {str(e)}")
                continue

        logger.info("Tier 2 bi-weekly follow-up scheduler completed")

    except Exception as e:
        logger.error(f"Error in Tier 2 bi-weekly follow-up scheduler: {str(e)}")

def get_tier2_leads_for_followup():
    """
    Get all Tier 2 leads that are eligible for bi-weekly follow-up

    Returns:
        List[Dict]: List of Tier 2 leads
    """
    try:
        # Query for Tier 2 leads that are not in DNC and are active
        res = supabase.table("leads")\
            .select("id, first_name, last_name, email, phone, ghl_contact_id, last_tier2_followup, tier2_followup_count, offer_made_date, created_at")\
            .eq("tier", 2)\
            .eq("dnc", False)\
            .not_.in_("status", ["closed", "disqualified"])\
            .gte("created_at", (datetime.utcnow() - timedelta(days=180)).isoformat())\
            .execute()

        return res.data or []

    except Exception as e:
        logger.error(f"Error getting Tier 2 leads: {str(e)}")
        return []

def should_send_tier2_followup(lead):
    """
    Determine if it's time to send a Tier 2 follow-up

    Args:
        lead (Dict): Lead data

    Returns:
        bool: True if follow-up should be sent
    """
    try:
        # Get last follow-up timestamp
        last_followup = lead.get('last_tier2_followup')
        if not last_followup:
            # If no previous follow-up, check if it's been at least 14 days since offer was made
            offer_date = lead.get('offer_made_date')
            if offer_date:
                offer_datetime = datetime.fromisoformat(offer_date.replace('Z', '+00:00'))
                return datetime.utcnow() >= offer_datetime + timedelta(days=14)
            return True  # Send first follow-up if no offer date

        # Check if 14 days have passed since last follow-up
        last_followup_datetime = datetime.fromisoformat(last_followup.replace('Z', '+00:00'))
        return datetime.utcnow() >= last_followup_datetime + timedelta(days=14)

    except Exception as e:
        logger.error(f"Error determining Tier 2 follow-up timing: {str(e)}")
        return False

def send_tier2_biweekly_checkin(lead_id, lead_data):
    """
    Send bi-weekly check-in to Tier 2 agent

    Args:
        lead_id (str): Lead ID
        lead_data (Dict): Lead data

    Returns:
        Dict: Result of communication attempt
    """
    try:
        logger.info(f"Sending Tier 2 bi-weekly check-in for lead {lead_id}")

        # Use the agent communication workflow with Tier 2 templates
        # Modify lead data to indicate this is a Tier 2 follow-up
        tier2_lead_data = lead_data.copy()
        tier2_lead_data['communication_type'] = 'tier2_biweekly'
        tier2_lead_data['tier'] = 2

        # Execute communication workflow
        result = execute_agent_communication(lead_id, tier2_lead_data)

        return result

    except Exception as e:
        logger.error(f"Error sending Tier 2 bi-weekly check-in: {str(e)}")
        return {"success": False, "error": str(e)}

def update_tier2_followup_timestamp(lead_id):
    """
    Update the last follow-up timestamp for a Tier 2 lead

    Args:
        lead_id (str): Lead ID
    """
    try:
        # Update the lead record with new timestamp
        supabase.table("leads")\
            .update({
                "last_tier2_followup": datetime.utcnow().isoformat(),
                "tier2_followup_count": supabase.table("leads").select("tier2_followup_count").eq("id", lead_id).execute().data[0].get("tier2_followup_count", 0) + 1
            })\
            .eq("id", lead_id)\
            .execute()

        logger.info(f"Updated Tier 2 follow-up timestamp for lead {lead_id}")

    except Exception as e:
        logger.error(f"Error updating Tier 2 follow-up timestamp: {str(e)}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "tier2":
        run_tier2_biweekly_scheduler()
    else:
        run_scheduler()


