{"lead_ingestion": {"enabled": true, "webhook_path": "/webhook/lead", "timeout_seconds": 60, "tiers": {"tier1": {"auto_process": true, "workflows": ["comping", "mao_calculation", "exit_strategy"]}, "tier2": {"auto_process": false, "workflows": []}}, "retry": {"max_attempts": 3, "backoff_seconds": 5}}, "comping": {"enabled": true, "sources": {"batchleads": {"priority": 1, "enabled": true, "timeout_seconds": 30, "max_retries": 3, "weight": 0.7}, "privy": {"priority": 2, "enabled": true, "timeout_seconds": 30, "max_retries": 3, "weight": 0.6}, "propwire": {"priority": 3, "enabled": true, "timeout_seconds": 30, "max_retries": 3, "weight": 0.5}, "alternative_sources": {"priority": 4, "enabled": true, "scraping_enabled": true, "timeout_seconds": 60, "max_retries": 2, "weight": 0.3}}, "business_rules": {"max_distance_miles": 1.0, "max_age_days": 180, "max_sqft_diff_percent": 20, "min_comps_required": 3, "preferred_comps_count": 5, "max_comps_to_use": 10, "adjust_for_age": true, "adjust_for_condition": true, "adjust_for_size": true, "size_adjustment_rate": 0.1, "age_adjustment_rate": 0.005}, "parallel_execution": true}, "mao_calculation": {"enabled": true, "parameters": {"profit_margin": 0.2, "holding_cost_percent": 0.02, "closing_cost_percent": 0.03, "contingency_percent": 0.05}, "repair_cost_factors": {"poor": 40, "fair": 25, "good": 15, "excellent": 5}, "repair_cost_adjustments": {"age_multiplier": {"pre_1950": 1.3, "1950_1980": 1.2, "1980_2000": 1.1, "post_2000": 1.0}, "special_features": {"pool": 5000, "large_lot": 2000, "multiple_stories": 3000}}, "notification_on_completion": true}, "exit_strategy": {"enabled": true, "strategies": {"wholesale": {"min_arv": 100000, "min_equity_percent": 0.25, "max_repair_percent": 0.15, "priority": 3}, "fix_and_flip": {"min_arv": 200000, "min_profit": 30000, "max_repair_percent": 0.25, "min_roi": 0.15, "priority": 2}, "buy_and_hold": {"min_cash_flow": 300, "min_cap_rate": 0.06, "max_repair_percent": 0.2, "priority": 1}, "subject_to": {"min_equity": 20000, "max_ltv": 0.8, "priority": 4}}, "buyer_matching": {"enabled": true, "max_matches": 10, "match_factors": {"location_weight": 0.3, "price_range_weight": 0.2, "strategy_weight": 0.3, "property_type_weight": 0.1, "condition_weight": 0.1}, "score_thresholds": {"high_match": 0.8, "medium_match": 0.6, "low_match": 0.4}}}, "follow_up": {"enabled": true, "schedule": {"initial": {"days": 2, "priority": "high"}, "reminder": {"days": 5, "priority": "medium"}, "final": {"days": 10, "priority": "low"}}, "max_follow_ups": 5, "time_window": {"start_hour": 9, "end_hour": 18, "timezone": "America/New_York", "weekdays_only": true}, "randomize_timing": true, "randomize_templates": true}, "disposition": {"enabled": true, "schedule": {"initial_offer": {"days": 0, "priority": "high"}, "first_follow_up": {"days": 3, "priority": "medium"}, "second_follow_up": {"days": 7, "priority": "medium"}, "final_follow_up": {"days": 14, "priority": "low"}}, "time_window": {"start_hour": 9, "end_hour": 18, "timezone": "America/New_York", "weekdays_only": true}, "randomize_timing": true, "randomize_templates": true}, "approval_workflow": {"enabled": true, "default_timeout_seconds": 86400, "notification_types": {"approval_needed": "sms", "approved": "sms", "rejected": "sms", "timed_out": "sms"}, "critical_approvals": ["offer_submission", "price_adjustment", "contract_signing"]}, "agent_communication": {"enabled": true, "hot_lead_processing": {"immediate_email": true, "immediate_sms": true, "notify_eric": true, "priority": "critical"}, "tier_2_follow_up": {"frequency_days": 14, "communication_types": ["email", "sms"], "max_attempts": 10, "priority": "medium"}, "tier_3_dnc": {"stop_all_communication": true, "add_to_dnc_list": true, "notify_team": false}, "templates": {"hot_lead_email": "hot_lead_immediate_interest_email", "hot_lead_sms": "hot_lead_immediate_sms", "tier2_email": "tier2_biweekly_checkin_email", "tier2_sms": "tier2_biweekly_checkin_sms"}, "eric_notifications": {"enabled": true, "contact_id": "ERIC_CONTACT_ID_PLACEHOLDER", "hot_lead_alerts": true, "call_requests": true, "agent_responses": true}}, "hot_lead_processing": {"enabled": true, "immediate_actions": ["send_email", "send_sms", "notify_eric", "trigger_comping", "calculate_mao"], "timeline": {"initial_contact": "immediate", "follow_up": "2_hours", "offer_generation": "24_hours"}, "escalation": {"no_response_hours": 4, "escalate_to_eric": true, "request_phone_call": true}}}