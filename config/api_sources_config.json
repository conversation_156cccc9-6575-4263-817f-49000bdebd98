{"api_sources": {"rentcast": {"name": "RentCast API (formerly RealtyMole)", "description": "Comprehensive property data, comparables, and valuations", "base_url": "https://api.rentcast.io/v1", "endpoints": {"property_records": "/properties", "value_estimate": "/avm/value", "rent_estimate": "/avm/rent/long-term", "sale_listings": "/listings/sale", "rental_listings": "/listings/rental/long-term", "market_statistics": "/markets"}, "auth": {"type": "api_key", "header": "X-Api-Key", "env_var": "RENTCAST_API_KEY"}, "rate_limits": {"requests_per_minute": 60, "requests_per_month": "plan_dependent"}, "pricing": {"developer_free": "50 requests/month free", "starter": "$39/month for 500 requests", "professional": "$99/month for 2000 requests", "business": "$299/month for 10000 requests", "overage_fee": "$0.10 per additional request"}, "data_quality": "very_high", "coverage": "nationwide_us", "features": ["Property records and history", "Automated valuation models (AVM)", "Comparable sales data", "Rental estimates", "Market statistics", "Property listings"]}, "rentspree": {"name": "RentSpree API", "description": "Rental and property data", "base_url": "https://api.rentspree.com/v1", "endpoints": {"properties": "/properties/search", "market_data": "/market/analysis"}, "auth": {"type": "bearer_token", "env_var": "RENTSPREE_API_KEY"}, "rate_limits": {"requests_per_minute": 100}, "data_quality": "medium", "coverage": "major_markets"}, "smarty_streets": {"name": "SmartyStreets API", "description": "Address validation and property data", "base_url": "https://us-street.api.smartystreets.com", "endpoints": {"street_address": "/street-address", "property_data": "/property/principal"}, "auth": {"type": "auth_id_token", "auth_id_env": "SMARTY_STREETS_AUTH_ID", "auth_token_env": "SMARTY_STREETS_AUTH_TOKEN"}, "rate_limits": {"requests_per_second": 10}, "pricing": {"pay_per_use": "$0.30 per lookup"}, "data_quality": "very_high", "coverage": "nationwide_us"}, "property_radar": {"name": "PropertyRadar API", "description": "Property records and market data", "base_url": "https://api.propertyradar.com/v1", "endpoints": {"property_search": "/properties/search", "comparables": "/properties/comparables", "market_trends": "/market/trends"}, "auth": {"type": "api_key", "header": "Authorization", "env_var": "PROPERTY_RADAR_API_KEY"}, "data_quality": "high", "coverage": "california_focused"}, "attom_data": {"name": "ATTOM Data API", "description": "Comprehensive property data", "base_url": "https://api.gateway.attomdata.com/propertyapi/v1.0.0", "endpoints": {"property_detail": "/property/detail", "comparable_sales": "/sale/snapshot", "avm": "/avm"}, "auth": {"type": "api_key", "header": "apikey", "env_var": "ATTOM_DATA_API_KEY"}, "rate_limits": {"requests_per_minute": 60}, "pricing": {"enterprise": "Contact for pricing"}, "data_quality": "very_high", "coverage": "nationwide_us"}, "bridge_interactive": {"name": "Bridge Interactive API", "description": "MLS and property data", "base_url": "https://api.bridgeinteractive.com/v2", "endpoints": {"property_search": "/properties", "comparables": "/comparables"}, "auth": {"type": "oauth2", "env_var": "BRIDGE_INTERACTIVE_TOKEN"}, "data_quality": "high", "coverage": "mls_dependent"}}, "fallback_sources": {"public_records": {"county_assessor_apis": [{"name": "Los Angeles County", "url": "https://portal.assessor.lacounty.gov/api", "coverage": "los_angeles_county"}, {"name": "Cook County (Chicago)", "url": "https://datacatalog.cookcountyil.gov/api", "coverage": "cook_county_il"}]}, "zillow_alternatives": {"description": "APIs that provide similar data to Zillow", "sources": ["realty_mole", "rentspree", "property_radar"]}}, "scraping_targets": {"last_resort_only": {"batchleads": {"difficulty": "medium", "success_rate": "75%", "session_duration": "2_hours", "recommended": true}, "privy": {"difficulty": "high", "success_rate": "60%", "session_duration": "1_hour", "recommended": false}, "lotside": {"difficulty": "medium", "success_rate": "70%", "session_duration": "1.5_hours", "recommended": true}}}, "data_aggregation_strategy": {"primary_sources": ["rentcast", "smarty_streets"], "secondary_sources": ["attom_data", "property_radar"], "fallback_scraping": ["batchleads", "lotside"], "minimum_comps_required": 3, "preferred_comps_count": 5, "max_api_calls_per_property": 2, "cache_duration_hours": 24}, "cost_optimization": {"api_cost_per_property": {"rentcast": 0.1, "smarty_streets": 0.3, "attom_data": 0.5}, "scraping_cost_per_property": {"time_cost_minutes": 2, "infrastructure_cost": 0.01}, "recommended_strategy": "rentcast_first_with_selective_scraping"}}